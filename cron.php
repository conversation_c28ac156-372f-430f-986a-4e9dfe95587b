<?php
/**
 * Cron 任務執行腳本
 * 
 * 使用方法：
 * php cron.php check-expiring     # 檢查即將到期的租戶
 * php cron.php auto-renewal       # 處理自動續租
 * php cron.php suspend-expired    # 暫停過期租戶
 * php cron.php cleanup-orders     # 清理過期訂單
 * php cron.php all                # 執行所有任務
 */

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 定義應用程式路徑
define('APPLICATION_PATH', __DIR__ . '/application');

// 載入自動續租類別
require_once APPLICATION_PATH . '/cron/AutoRenewal.php';

// 獲取命令行參數
$task = $argv[1] ?? 'help';

// 創建 Cron 實例
$cron = new AutoRenewalCron();

echo "=== 汽車保養廠管理系統 Cron 任務 ===\n";
echo "執行時間: " . date('Y-m-d H:i:s') . "\n";
echo "執行任務: {$task}\n";
echo "=====================================\n\n";

try {
    switch ($task) {
        case 'check-expiring':
            $cron->checkExpiring();
            break;
            
        case 'auto-renewal':
            $cron->processAutoRenewal();
            break;
            
        case 'suspend-expired':
            $cron->suspendExpiredTenants();
            break;
            
        case 'cleanup-orders':
            $cron->cleanupExpiredOrders();
            break;
            
        case 'all':
            echo "執行所有 Cron 任務...\n\n";
            
            echo "1. 檢查即將到期的租戶\n";
            $cron->checkExpiring();
            echo "\n";
            
            echo "2. 處理自動續租\n";
            $cron->processAutoRenewal();
            echo "\n";
            
            echo "3. 暫停過期租戶\n";
            $cron->suspendExpiredTenants();
            echo "\n";
            
            echo "4. 清理過期訂單\n";
            $cron->cleanupExpiredOrders();
            echo "\n";
            
            echo "所有任務執行完成\n";
            break;
            
        case 'help':
        default:
            echo "可用的 Cron 任務:\n";
            echo "  check-expiring   - 檢查即將到期的租戶\n";
            echo "  auto-renewal     - 處理自動續租\n";
            echo "  suspend-expired  - 暫停過期租戶\n";
            echo "  cleanup-orders   - 清理過期訂單\n";
            echo "  all              - 執行所有任務\n";
            echo "  help             - 顯示此幫助訊息\n\n";
            echo "使用方法: php cron.php [task]\n";
            break;
    }
    
} catch (Exception $e) {
    echo "Cron 任務執行失敗: " . $e->getMessage() . "\n";
    echo "錯誤追蹤: " . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n=====================================\n";
echo "任務執行完成: " . date('Y-m-d H:i:s') . "\n";
echo "=====================================\n";

<?php
/**
 * 預約模型
 */
class AppointmentModel extends BaseModel
{
    protected $table = 'appointments';
    
    protected $fillable = [
        'appointment_no', 'customer_id', 'vehicle_id', 'technician_id',
        'appointment_date', 'appointment_time', 'estimated_duration',
        'service_type', 'description', 'customer_notes', 'status', 'reminder_sent'
    ];

    /**
     * 生成預約編號
     */
    public function generateAppointmentNo()
    {
        $tenantId = $this->getCurrentTenantId();
        $prefix = 'AP' . date('Ymd');
        
        // 獲取當日最大編號
        $stmt = $this->db->prepare("
            SELECT appointment_no 
            FROM appointments 
            WHERE tenant_id = ? 
            AND appointment_no LIKE ? 
            ORDER BY appointment_no DESC 
            LIMIT 1
        ");
        $stmt->execute([$tenantId, $prefix . '%']);
        $lastAppointment = $stmt->fetch();
        
        if ($lastAppointment) {
            $lastNo = intval(substr($lastAppointment['appointment_no'], -3));
            $newNo = $lastNo + 1;
        } else {
            $newNo = 1;
        }
        
        return $prefix . str_pad($newNo, 3, '0', STR_PAD_LEFT);
    }

    /**
     * 創建預約
     */
    public function create($data)
    {
        // 自動生成預約編號
        if (empty($data['appointment_no'])) {
            $data['appointment_no'] = $this->generateAppointmentNo();
        }
        
        return parent::create($data);
    }

    /**
     * 獲取本週預約
     */
    public function getWeekAppointments($limit = null)
    {
        $sql = "
            SELECT a.*, c.name as customer_name, c.phone as customer_phone,
                   v.license_plate, u.full_name as technician_name
            FROM appointments a
            LEFT JOIN customers c ON a.customer_id = c.id
            LEFT JOIN vehicles v ON a.vehicle_id = v.id
            LEFT JOIN users u ON a.technician_id = u.id
            WHERE a.tenant_id = ?
            AND a.appointment_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
            ORDER BY a.appointment_date ASC, a.appointment_time ASC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->getCurrentTenantId()]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取今日預約
     */
    public function getTodayAppointments($limit = null)
    {
        $sql = "
            SELECT a.*, c.name as customer_name, c.phone as customer_phone,
                   v.license_plate, u.full_name as technician_name
            FROM appointments a
            LEFT JOIN customers c ON a.customer_id = c.id
            LEFT JOIN vehicles v ON a.vehicle_id = v.id
            LEFT JOIN users u ON a.technician_id = u.id
            WHERE a.tenant_id = ?
            AND a.appointment_date = CURDATE()
            ORDER BY a.appointment_time ASC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->getCurrentTenantId()]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取車輛預約記錄
     */
    public function getVehicleAppointments($vehicleId, $limit = null)
    {
        $sql = "
            SELECT a.*, c.name as customer_name, u.full_name as technician_name
            FROM appointments a
            LEFT JOIN customers c ON a.customer_id = c.id
            LEFT JOIN users u ON a.technician_id = u.id
            WHERE a.vehicle_id = ?
            ORDER BY a.appointment_date DESC, a.appointment_time DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$vehicleId]);
        
        return $stmt->fetchAll();
    }

    /**
     * 檢查時間衝突
     */
    public function checkTimeConflict($date, $time, $duration, $technicianId = null, $excludeId = null)
    {
        $startTime = $time;
        $endTime = date('H:i:s', strtotime($time) + ($duration * 60));
        
        $sql = "
            SELECT COUNT(*) as count
            FROM appointments
            WHERE tenant_id = ?
            AND appointment_date = ?
            AND status = 'confirmed'
            AND (
                (appointment_time <= ? AND DATE_ADD(CONCAT(appointment_date, ' ', appointment_time), INTERVAL estimated_duration MINUTE) > ?) OR
                (appointment_time < ? AND DATE_ADD(CONCAT(appointment_date, ' ', appointment_time), INTERVAL estimated_duration MINUTE) >= ?)
            )
        ";
        
        $params = [$this->getCurrentTenantId(), $date, $startTime, $startTime, $endTime, $endTime];
        
        // 如果指定技師，檢查技師衝突
        if ($technicianId) {
            $sql .= " AND technician_id = ?";
            $params[] = $technicianId;
        }
        
        // 排除當前預約（用於更新時）
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }

    /**
     * 獲取可用時間段
     */
    public function getAvailableTimeSlots($date, $technicianId = null, $duration = 60)
    {
        // 營業時間設定（可以從系統設定中獲取）
        $workStart = '08:00';
        $workEnd = '18:00';
        $slotDuration = 30; // 30分鐘一個時段
        
        $availableSlots = [];
        $currentTime = strtotime($date . ' ' . $workStart);
        $endTime = strtotime($date . ' ' . $workEnd);
        
        while ($currentTime < $endTime) {
            $timeSlot = date('H:i:s', $currentTime);
            
            // 檢查是否有衝突
            if (!$this->checkTimeConflict($date, $timeSlot, $duration, $technicianId)) {
                $availableSlots[] = $timeSlot;
            }
            
            $currentTime += ($slotDuration * 60);
        }
        
        return $availableSlots;
    }

    /**
     * 獲取預約統計
     */
    public function getStats($period = 'month')
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stats = [];
        
        // 根據期間設定日期條件
        switch ($period) {
            case 'today':
                $dateCondition = "appointment_date = CURDATE()";
                break;
            case 'week':
                $dateCondition = "appointment_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $dateCondition = "appointment_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
                break;
            default:
                $dateCondition = "1=1";
        }
        
        // 按狀態統計
        $stmt = $this->db->prepare("
            SELECT status, COUNT(*) as count 
            FROM appointments 
            WHERE tenant_id = ? AND {$dateCondition}
            GROUP BY status
        ");
        $stmt->execute([$tenantId]);
        $statusStats = $stmt->fetchAll();
        $stats['by_status'] = [];
        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = $stat['count'];
        }
        
        // 按技師統計
        $stmt = $this->db->prepare("
            SELECT u.full_name as technician_name, COUNT(*) as count 
            FROM appointments a
            LEFT JOIN users u ON a.technician_id = u.id
            WHERE a.tenant_id = ? AND {$dateCondition}
            AND a.technician_id IS NOT NULL
            GROUP BY a.technician_id, u.full_name
            ORDER BY count DESC
        ");
        $stmt->execute([$tenantId]);
        $stats['by_technician'] = $stmt->fetchAll();
        
        // 按服務類型統計
        $stmt = $this->db->prepare("
            SELECT service_type, COUNT(*) as count 
            FROM appointments 
            WHERE tenant_id = ? AND {$dateCondition}
            AND service_type IS NOT NULL
            GROUP BY service_type
            ORDER BY count DESC
        ");
        $stmt->execute([$tenantId]);
        $stats['by_service_type'] = $stmt->fetchAll();
        
        return $stats;
    }

    /**
     * 獲取需要提醒的預約
     */
    public function getAppointmentsNeedingReminder($hours = 24)
    {
        $sql = "
            SELECT a.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email,
                   v.license_plate
            FROM appointments a
            LEFT JOIN customers c ON a.customer_id = c.id
            LEFT JOIN vehicles v ON a.vehicle_id = v.id
            WHERE a.tenant_id = ?
            AND a.status = 'confirmed'
            AND a.reminder_sent = 0
            AND CONCAT(a.appointment_date, ' ', a.appointment_time) BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? HOUR)
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->getCurrentTenantId(), $hours]);
        
        return $stmt->fetchAll();
    }

    /**
     * 標記提醒已發送
     */
    public function markReminderSent($appointmentId)
    {
        return $this->update($appointmentId, ['reminder_sent' => 1]);
    }

    /**
     * 取消預約
     */
    public function cancelAppointment($appointmentId, $reason = null)
    {
        $data = ['status' => 'cancelled'];
        if ($reason) {
            $data['cancel_reason'] = $reason;
        }
        return $this->update($appointmentId, $data);
    }

    /**
     * 完成預約
     */
    public function completeAppointment($appointmentId)
    {
        return $this->update($appointmentId, ['status' => 'completed']);
    }

    /**
     * 標記為未出現
     */
    public function markNoShow($appointmentId)
    {
        return $this->update($appointmentId, ['status' => 'no_show']);
    }
}

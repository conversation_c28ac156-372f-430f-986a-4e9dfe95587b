<?php
class RegisterController extends Yaf_Controller_Abstract {
    
    public function indexAction() {
        // 顯示註冊表單
    }
    
    public function createAction() {
        $request = $this->getRequest();
        
        // 建立租戶
        $tenantData = [
            'name' => $request->getPost('garage_name'),
            'subdomain' => $request->getPost('subdomain'),
            'trial_expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
        ];
        
        $tenantModel = new TenantModel();
        $tenantId = $tenantModel->create($tenantData);
        
        // 建立管理員帳號
        $this->createOwnerAccount($tenantId, $request->getPost());
        
        $this->redirect('/tenant/dashboard');
    }
}
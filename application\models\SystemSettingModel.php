<?php
/**
 * 系統設定模型
 */
class SystemSettingModel extends BaseModel
{
    protected $table = 'system_settings';
    
    protected $fillable = [
        'setting_key', 'setting_value', 'setting_type', 'description'
    ];

    /**
     * 獲取租戶的所有設定
     */
    public function getTenantSettings($tenantId)
    {
        $stmt = $this->db->prepare("
            SELECT setting_key, setting_value, setting_type, description
            FROM system_settings
            WHERE tenant_id = ?
            ORDER BY setting_key ASC
        ");
        $stmt->execute([$tenantId]);
        $results = $stmt->fetchAll();
        
        $settings = [];
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $this->castValue($row['setting_value'], $row['setting_type']);
        }
        
        return $settings;
    }

    /**
     * 獲取單個設定值
     */
    public function getSetting($tenantId, $key, $default = null)
    {
        $stmt = $this->db->prepare("
            SELECT setting_value, setting_type
            FROM system_settings
            WHERE tenant_id = ? AND setting_key = ?
        ");
        $stmt->execute([$tenantId, $key]);
        $result = $stmt->fetch();
        
        if (!$result) {
            return $default;
        }
        
        return $this->castValue($result['setting_value'], $result['setting_type']);
    }

    /**
     * 設定值
     */
    public function setSetting($tenantId, $key, $value, $type = null, $description = null)
    {
        // 自動判斷類型
        if ($type === null) {
            $type = $this->detectType($value);
        }
        
        // 轉換值為字串存儲
        $stringValue = $this->valueToString($value, $type);
        
        // 檢查設定是否已存在
        $existing = $this->db->prepare("
            SELECT id FROM system_settings 
            WHERE tenant_id = ? AND setting_key = ?
        ");
        $existing->execute([$tenantId, $key]);
        
        if ($existing->fetch()) {
            // 更新現有設定
            $stmt = $this->db->prepare("
                UPDATE system_settings 
                SET setting_value = ?, setting_type = ?, description = ?, updated_at = NOW()
                WHERE tenant_id = ? AND setting_key = ?
            ");
            return $stmt->execute([$stringValue, $type, $description, $tenantId, $key]);
        } else {
            // 創建新設定
            $data = [
                'tenant_id' => $tenantId,
                'setting_key' => $key,
                'setting_value' => $stringValue,
                'setting_type' => $type,
                'description' => $description
            ];
            return $this->create($data);
        }
    }

    /**
     * 刪除設定
     */
    public function deleteSetting($tenantId, $key)
    {
        $stmt = $this->db->prepare("
            DELETE FROM system_settings 
            WHERE tenant_id = ? AND setting_key = ?
        ");
        return $stmt->execute([$tenantId, $key]);
    }

    /**
     * 批量設定
     */
    public function batchSetSettings($tenantId, $settings)
    {
        $this->beginTransaction();
        
        try {
            foreach ($settings as $key => $value) {
                $this->setSetting($tenantId, $key, $value);
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 獲取預設設定
     */
    public function getDefaultSettings()
    {
        return [
            // 基本設定
            'company_name' => '',
            'company_address' => '',
            'company_phone' => '',
            'company_email' => '',
            'company_website' => '',
            
            // 營業時間
            'business_hours_start' => '08:00',
            'business_hours_end' => '18:00',
            'business_days' => 'monday,tuesday,wednesday,thursday,friday,saturday',
            
            // 預約設定
            'appointment_duration' => 60,
            'appointment_advance_days' => 30,
            'appointment_reminder_hours' => 24,
            
            // 工單設定
            'work_order_auto_number' => true,
            'work_order_require_signature' => true,
            'work_order_default_warranty_days' => 30,
            
            // 庫存設定
            'inventory_low_stock_alert' => true,
            'inventory_auto_deduct' => true,
            
            // 通知設定
            'notification_email' => true,
            'notification_sms' => false,
            'notification_system' => true,
            
            // 列印設定
            'print_auto_open' => true,
            'print_copies' => 1,
            'print_paper_size' => 'A4',
            
            // 財務設定
            'tax_rate' => 5.0,
            'currency' => 'TWD',
            'invoice_auto_number' => true,
            
            // 安全設定
            'session_timeout' => 7200,
            'password_min_length' => 6,
            'login_max_attempts' => 5,
        ];
    }

    /**
     * 初始化租戶預設設定
     */
    public function initializeTenantSettings($tenantId)
    {
        $defaultSettings = $this->getDefaultSettings();
        return $this->batchSetSettings($tenantId, $defaultSettings);
    }

    /**
     * 自動檢測值的類型
     */
    private function detectType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'number';
        } elseif (is_float($value)) {
            return 'number';
        } elseif (is_array($value) || is_object($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }

    /**
     * 將值轉換為字串存儲
     */
    private function valueToString($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'number':
                return (string)$value;
            case 'json':
                return json_encode($value, JSON_UNESCAPED_UNICODE);
            default:
                return (string)$value;
        }
    }

    /**
     * 將字串值轉換為對應類型
     */
    private function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value === '1' || $value === 'true';
            case 'number':
                return is_numeric($value) ? ($value == (int)$value ? (int)$value : (float)$value) : 0;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * 獲取設定分組
     */
    public function getSettingGroups($tenantId)
    {
        $settings = $this->getTenantSettings($tenantId);
        
        $groups = [
            'basic' => [
                'title' => '基本設定',
                'settings' => []
            ],
            'business' => [
                'title' => '營業設定',
                'settings' => []
            ],
            'appointment' => [
                'title' => '預約設定',
                'settings' => []
            ],
            'work_order' => [
                'title' => '工單設定',
                'settings' => []
            ],
            'inventory' => [
                'title' => '庫存設定',
                'settings' => []
            ],
            'notification' => [
                'title' => '通知設定',
                'settings' => []
            ],
            'print' => [
                'title' => '列印設定',
                'settings' => []
            ],
            'financial' => [
                'title' => '財務設定',
                'settings' => []
            ],
            'security' => [
                'title' => '安全設定',
                'settings' => []
            ]
        ];
        
        foreach ($settings as $key => $value) {
            $group = $this->getSettingGroup($key);
            if (isset($groups[$group])) {
                $groups[$group]['settings'][$key] = $value;
            }
        }
        
        return $groups;
    }

    /**
     * 根據設定鍵獲取分組
     */
    private function getSettingGroup($key)
    {
        $groupMap = [
            'company_' => 'basic',
            'business_' => 'business',
            'appointment_' => 'appointment',
            'work_order_' => 'work_order',
            'inventory_' => 'inventory',
            'notification_' => 'notification',
            'print_' => 'print',
            'tax_' => 'financial',
            'currency' => 'financial',
            'invoice_' => 'financial',
            'session_' => 'security',
            'password_' => 'security',
            'login_' => 'security'
        ];
        
        foreach ($groupMap as $prefix => $group) {
            if (strpos($key, $prefix) === 0) {
                return $group;
            }
        }
        
        return 'basic';
    }

    /**
     * 匯出設定
     */
    public function exportSettings($tenantId)
    {
        $settings = $this->getTenantSettings($tenantId);
        return json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 匯入設定
     */
    public function importSettings($tenantId, $jsonData)
    {
        $settings = json_decode($jsonData, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('無效的 JSON 格式');
        }
        
        return $this->batchSetSettings($tenantId, $settings);
    }
}

-- 補充列印功能相關資料表
USE cars_manage;

-- ================================
-- 列印範本管理
-- ================================

-- 列印範本表
CREATE TABLE print_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    template_type ENUM('work_order_confirm', 'work_order_complete', 'invoice', 'estimate') NOT NULL COMMENT '範本類型',
    template_name VARCHAR(100) NOT NULL COMMENT '範本名稱',
    template_content LONGTEXT NOT NULL COMMENT '範本內容(HTML)',
    paper_size ENUM('A4', 'A5', 'thermal_80mm', 'thermal_58mm') DEFAULT 'A4' COMMENT '紙張尺寸',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否為預設範本',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant_type (tenant_id, template_type)
) COMMENT '列印範本表';

-- 列印記錄表
CREATE TABLE print_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    user_id INT NOT NULL,
    print_type ENUM('work_order_confirm', 'work_order_complete', 'invoice', 'estimate') NOT NULL,
    reference_id INT NOT NULL COMMENT '關聯ID(工單ID或發票ID)',
    template_id INT COMMENT '使用的範本ID',
    print_count INT DEFAULT 1 COMMENT '列印份數',
    print_data JSON COMMENT '列印時的資料快照',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (template_id) REFERENCES print_templates(id),
    INDEX idx_tenant_type_ref (tenant_id, print_type, reference_id)
) COMMENT '列印記錄表';

-- ================================
-- 發票管理
-- ================================

-- 發票表
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    work_order_id INT,
    invoice_no VARCHAR(30) UNIQUE NOT NULL COMMENT '發票號碼',
    invoice_type ENUM('receipt', 'invoice', 'e_invoice') DEFAULT 'receipt' COMMENT '發票類型',
    customer_id INT NOT NULL,
    vehicle_id INT,
    issue_date DATE NOT NULL COMMENT '開立日期',
    due_date DATE COMMENT '到期日',
    
    -- 客戶資訊快照
    customer_name VARCHAR(100) NOT NULL,
    customer_phone VARCHAR(20),
    customer_address TEXT,
    customer_tax_id VARCHAR(20) COMMENT '客戶統編',
    
    -- 車輛資訊快照
    vehicle_info VARCHAR(200) COMMENT '車輛資訊',
    
    -- 金額計算
    subtotal DECIMAL(10,2) DEFAULT 0 COMMENT '小計',
    tax_rate DECIMAL(5,2) DEFAULT 5.00 COMMENT '稅率%',
    tax_amount DECIMAL(10,2) DEFAULT 0 COMMENT '稅額',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '折扣金額',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '總金額',
    
    -- 狀態管理
    status ENUM('draft', 'issued', 'paid', 'cancelled', 'void') DEFAULT 'draft',
    payment_status ENUM('unpaid', 'partial', 'paid', 'overdue') DEFAULT 'unpaid',
    
    -- 備註
    notes TEXT COMMENT '備註',
    internal_notes TEXT COMMENT '內部備註',
    
    -- 電子發票相關
    e_invoice_number VARCHAR(50) COMMENT '電子發票號碼',
    e_invoice_random_code VARCHAR(10) COMMENT '電子發票隨機碼',
    e_invoice_qr_code TEXT COMMENT 'QR Code內容',
    
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_tenant_date (tenant_id, issue_date),
    INDEX idx_tenant_status (tenant_id, status),
    INDEX idx_work_order (work_order_id)
) COMMENT '發票表';

-- 發票明細表
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    item_type ENUM('labor', 'part', 'service', 'other') NOT NULL COMMENT '項目類型',
    item_name VARCHAR(200) NOT NULL COMMENT '項目名稱',
    item_description TEXT COMMENT '項目說明',
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1 COMMENT '數量',
    unit VARCHAR(20) DEFAULT '項' COMMENT '單位',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '單價',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小計',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_type (invoice_id, item_type)
) COMMENT '發票明細表';

-- ================================
-- 預設範本資料
-- ================================

-- 插入預設列印範本
INSERT INTO print_templates (tenant_id, template_type, template_name, template_content, paper_size, is_default) VALUES
(1, 'work_order_confirm', '工單確認單', '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>工單確認單</title>
    <style>
        body { font-family: "Microsoft JhengHei", Arial, sans-serif; font-size: 12px; }
        .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
        .info-row { display: flex; margin: 5px 0; }
        .label { font-weight: bold; width: 100px; }
        .content { flex: 1; border-bottom: 1px dotted #ccc; }
        .items-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .items-table th, .items-table td { border: 1px solid #000; padding: 5px; text-align: left; }
        .footer { margin-top: 20px; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h2>{{garage_name}} - 工單確認單</h2>
        <p>工單編號：{{order_no}} | 日期：{{date}}</p>
    </div>
    
    <div class="customer-info">
        <div class="info-row">
            <span class="label">客戶姓名：</span>
            <span class="content">{{customer_name}}</span>
            <span class="label">聯絡電話：</span>
            <span class="content">{{customer_phone}}</span>
        </div>
        <div class="info-row">
            <span class="label">車牌號碼：</span>
            <span class="content">{{license_plate}}</span>
            <span class="label">車輛型號：</span>
            <span class="content">{{vehicle_model}}</span>
        </div>
        <div class="info-row">
            <span class="label">進廠里程：</span>
            <span class="content">{{mileage_in}} 公里</span>
            <span class="label">負責技師：</span>
            <span class="content">{{technician_name}}</span>
        </div>
    </div>
    
    <div class="work-content">
        <h3>工作內容：</h3>
        <p>{{description}}</p>
        <h3>客戶反映問題：</h3>
        <p>{{customer_complaint}}</p>
    </div>
    
    <div class="footer">
        <p>客戶簽名：_________________ 日期：_________</p>
        <p>{{garage_address}} | {{garage_phone}}</p>
    </div>
</body>
</html>
', 'A4', TRUE),

(1, 'work_order_complete', '完工工單', '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>完工工單</title>
    <style>
        body { font-family: "Microsoft JhengHei", Arial, sans-serif; font-size: 12px; }
        .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
        .info-section { margin: 10px 0; }
        .info-row { display: flex; margin: 3px 0; }
        .label { font-weight: bold; width: 100px; }
        .content { flex: 1; border-bottom: 1px dotted #ccc; }
        .parts-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .parts-table th, .parts-table td { border: 1px solid #000; padding: 5px; text-align: center; }
        .total-section { margin: 15px 0; text-align: right; }
        .total-row { margin: 5px 0; }
        .footer { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h2>{{garage_name}} - 完工工單</h2>
        <p>工單編號：{{order_no}} | 完工日期：{{completed_date}}</p>
    </div>
    
    <div class="info-section">
        <div class="info-row">
            <span class="label">客戶姓名：</span>
            <span class="content">{{customer_name}}</span>
            <span class="label">聯絡電話：</span>
            <span class="content">{{customer_phone}}</span>
        </div>
        <div class="info-row">
            <span class="label">車牌號碼：</span>
            <span class="content">{{license_plate}}</span>
            <span class="label">車輛型號：</span>
            <span class="content">{{vehicle_model}}</span>
        </div>
        <div class="info-row">
            <span class="label">進廠里程：</span>
            <span class="content">{{mileage_in}} 公里</span>
            <span class="label">出廠里程：</span>
            <span class="content">{{mileage_out}} 公里</span>
        </div>
        <div class="info-row">
            <span class="label">負責技師：</span>
            <span class="content">{{technician_name}}</span>
            <span class="label">工作時數：</span>
            <span class="content">{{actual_hours}} 小時</span>
        </div>
    </div>
    
    <div class="work-performed">
        <h3>執行工作：</h3>
        <p>{{work_performed}}</p>
    </div>
    
    <table class="parts-table">
        <thead>
            <tr>
                <th>零件名稱</th>
                <th>數量</th>
                <th>單價</th>
                <th>小計</th>
            </tr>
        </thead>
        <tbody>
            {{parts_list}}
        </tbody>
    </table>
    
    <div class="total-section">
        <div class="total-row">工資費用：NT$ {{labor_cost}}</div>
        <div class="total-row">零件費用：NT$ {{parts_cost}}</div>
        <div class="total-row">折扣：NT$ {{discount}}</div>
        <div class="total-row"><strong>總計：NT$ {{final_amount}}</strong></div>
    </div>
    
    <div class="footer">
        <p>客戶簽名：_________________ 日期：_________</p>
        <p>{{garage_address}} | {{garage_phone}}</p>
    </div>
</body>
</html>
', 'A4', TRUE),

(1, 'invoice', '發票', '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>發票</title>
    <style>
        body { font-family: "Microsoft JhengHei", Arial, sans-serif; font-size: 12px; }
        .invoice-header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
        .company-info { text-align: left; margin: 10px 0; }
        .customer-info { margin: 10px 0; border: 1px solid #000; padding: 10px; }
        .invoice-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .invoice-table th, .invoice-table td { border: 1px solid #000; padding: 8px; }
        .invoice-table th { background-color: #f0f0f0; text-align: center; }
        .total-section { margin: 15px 0; text-align: right; }
        .total-row { margin: 5px 0; font-size: 14px; }
        .payment-info { margin: 15px 0; }
        .footer { margin-top: 30px; text-align: center; font-size: 10px; }
    </style>
</head>
<body>
    <div class="invoice-header">
        <h2>{{garage_name}}</h2>
        <h3>發票 / INVOICE</h3>
        <p>發票號碼：{{invoice_no}} | 開立日期：{{issue_date}}</p>
    </div>
    
    <div class="company-info">
        <strong>{{garage_name}}</strong><br>
        統一編號：{{garage_tax_id}}<br>
        地址：{{garage_address}}<br>
        電話：{{garage_phone}}
    </div>
    
    <div class="customer-info">
        <strong>客戶資訊 Customer Information</strong><br>
        客戶姓名：{{customer_name}}<br>
        聯絡電話：{{customer_phone}}<br>
        地址：{{customer_address}}<br>
        {{#customer_tax_id}}統一編號：{{customer_tax_id}}<br>{{/customer_tax_id}}
        車輛：{{vehicle_info}}
    </div>
    
    <table class="invoice-table">
        <thead>
            <tr>
                <th>項目 Item</th>
                <th>說明 Description</th>
                <th>數量 Qty</th>
                <th>單價 Unit Price</th>
                <th>金額 Amount</th>
            </tr>
        </thead>
        <tbody>
            {{invoice_items}}
        </tbody>
    </table>
    
    <div class="total-section">
        <div class="total-row">小計 Subtotal：NT$ {{subtotal}}</div>
        <div class="total-row">營業稅 Tax ({{tax_rate}}%)：NT$ {{tax_amount}}</div>
        {{#discount_amount}}<div class="total-row">折扣 Discount：NT$ {{discount_amount}}</div>{{/discount_amount}}
        <div class="total-row"><strong>總計 Total：NT$ {{total_amount}}</strong></div>
    </div>
    
    <div class="payment-info">
        <p><strong>付款狀態：{{payment_status_text}}</strong></p>
        {{#notes}}<p>備註：{{notes}}</p>{{/notes}}
    </div>
    
    {{#e_invoice_qr_code}}
    <div class="qr-section" style="text-align: center; margin: 20px 0;">
        <p>電子發票QR Code</p>
        <img src="{{e_invoice_qr_code}}" alt="QR Code" style="width: 100px; height: 100px;">
    </div>
    {{/e_invoice_qr_code}}
    
    <div class="footer">
        <p>感謝您的惠顧 Thank you for your business!</p>
        <p>{{garage_address}} | {{garage_phone}}</p>
    </div>
</body>
</html>
', 'A4', TRUE);
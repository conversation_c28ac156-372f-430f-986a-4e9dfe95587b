<?php
/**
 * 活動日誌模型
 */
class ActivityLogModel extends BaseModel
{
    protected $table = 'activity_logs';
    
    protected $fillable = [
        'user_id', 'action', 'table_name', 'record_id', 
        'old_values', 'new_values', 'ip_address', 'user_agent'
    ];

    /**
     * 記錄活動
     */
    public function logActivity($action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null)
    {
        $tenant = Yaf_Registry::get('current_tenant');
        $user = Yaf_Registry::get('current_user');
        
        if (!$tenant || !$user) {
            return false;
        }
        
        $data = [
            'tenant_id' => $tenant['id'],
            'user_id' => $user['id'],
            'action' => $action,
            'table_name' => $tableName,
            'record_id' => $recordId,
            'old_values' => $oldValues ? json_encode($oldValues, JSON_UNESCAPED_UNICODE) : null,
            'new_values' => $newValues ? json_encode($newValues, JSON_UNESCAPED_UNICODE) : null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        return $this->create($data);
    }

    /**
     * 獲取活動日誌
     */
    public function getActivityLogs($filters = [], $page = 1, $perPage = 50)
    {
        $query = $this->query()
            ->select('al.*, u.full_name as user_name')
            ->leftJoin('users u', 'al.user_id = u.id')
            ->orderBy('al.created_at', 'DESC');
        
        // 應用篩選條件
        if (!empty($filters['user_id'])) {
            $query->where('al.user_id', $filters['user_id']);
        }
        
        if (!empty($filters['action'])) {
            $query->where('al.action', $filters['action']);
        }
        
        if (!empty($filters['table_name'])) {
            $query->where('al.table_name', $filters['table_name']);
        }
        
        if (!empty($filters['date_from'])) {
            $query->where('DATE(al.created_at) >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $query->where('DATE(al.created_at) <=', $filters['date_to']);
        }
        
        // 分頁
        $offset = ($page - 1) * $perPage;
        $query->limit($perPage)->offset($offset);
        
        $logs = $query->get();
        
        // 解析 JSON 數據
        foreach ($logs as &$log) {
            $log['old_values'] = $log['old_values'] ? json_decode($log['old_values'], true) : null;
            $log['new_values'] = $log['new_values'] ? json_decode($log['new_values'], true) : null;
        }
        
        return $logs;
    }

    /**
     * 獲取記錄的活動歷史
     */
    public function getRecordHistory($tableName, $recordId)
    {
        $stmt = $this->db->prepare("
            SELECT al.*, u.full_name as user_name
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.tenant_id = ? AND al.table_name = ? AND al.record_id = ?
            ORDER BY al.created_at DESC
        ");
        $stmt->execute([$this->getCurrentTenantId(), $tableName, $recordId]);
        $logs = $stmt->fetchAll();
        
        // 解析 JSON 數據
        foreach ($logs as &$log) {
            $log['old_values'] = $log['old_values'] ? json_decode($log['old_values'], true) : null;
            $log['new_values'] = $log['new_values'] ? json_decode($log['new_values'], true) : null;
        }
        
        return $logs;
    }

    /**
     * 獲取活動統計
     */
    public function getActivityStats($days = 30)
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stats = [];
        
        // 按動作統計
        $stmt = $this->db->prepare("
            SELECT action, COUNT(*) as count
            FROM activity_logs
            WHERE tenant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY action
            ORDER BY count DESC
        ");
        $stmt->execute([$tenantId, $days]);
        $stats['by_action'] = $stmt->fetchAll();
        
        // 按用戶統計
        $stmt = $this->db->prepare("
            SELECT u.full_name as user_name, COUNT(al.id) as count
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.tenant_id = ? AND al.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY al.user_id, u.full_name
            ORDER BY count DESC
            LIMIT 10
        ");
        $stmt->execute([$tenantId, $days]);
        $stats['by_user'] = $stmt->fetchAll();
        
        // 按日期統計
        $stmt = $this->db->prepare("
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM activity_logs
            WHERE tenant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$tenantId, $days]);
        $stats['by_date'] = $stmt->fetchAll();
        
        return $stats;
    }

    /**
     * 清理舊日誌
     */
    public function cleanupOldLogs($days = 90)
    {
        $stmt = $this->db->prepare("
            DELETE FROM activity_logs 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        
        return $stmt->rowCount();
    }

    /**
     * 獲取動作列表
     */
    public function getActionList()
    {
        return [
            'user_login' => '用戶登入',
            'user_logout' => '用戶登出',
            'user_create' => '創建用戶',
            'user_update' => '更新用戶',
            'user_delete' => '刪除用戶',
            'user_activate' => '啟用用戶',
            'user_deactivate' => '停用用戶',
            
            'customer_create' => '創建客戶',
            'customer_update' => '更新客戶',
            'customer_delete' => '刪除客戶',
            
            'vehicle_create' => '創建車輛',
            'vehicle_update' => '更新車輛',
            'vehicle_delete' => '刪除車輛',
            'vehicle_mileage_update' => '更新里程',
            
            'work_order_create' => '創建工單',
            'work_order_update' => '更新工單',
            'work_order_delete' => '刪除工單',
            'work_order_status_change' => '工單狀態變更',
            
            'appointment_create' => '創建預約',
            'appointment_update' => '更新預約',
            'appointment_delete' => '刪除預約',
            'appointment_cancel' => '取消預約',
            
            'part_create' => '創建零件',
            'part_update' => '更新零件',
            'part_delete' => '刪除零件',
            'part_stock_update' => '更新庫存',
            
            'payment_success' => '支付成功',
            'payment_failed' => '支付失敗',
            'payment_refund' => '申請退款',
            
            'tenant_register' => '租戶註冊',
            'tenant_settings_update' => '租戶設定更新',
            'system_settings_update' => '系統設定更新',
            'subscription_cancel' => '取消訂閱',
            'auto_renewal_failed' => '自動續租失敗'
        ];
    }

    /**
     * 格式化動作名稱
     */
    public function formatAction($action)
    {
        $actionList = $this->getActionList();
        return $actionList[$action] ?? $action;
    }

    /**
     * 匯出活動日誌
     */
    public function exportLogs($filters = [], $format = 'csv')
    {
        $logs = $this->getActivityLogs($filters, 1, 10000); // 最多匯出10000條
        
        if ($format === 'csv') {
            return $this->exportToCsv($logs);
        }
        
        return $logs;
    }

    /**
     * 匯出為 CSV
     */
    private function exportToCsv($logs)
    {
        $filename = '活動日誌_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // 輸出 BOM
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // 標題行
        fputcsv($output, [
            '時間', '用戶', '動作', '表名', '記錄ID', 'IP地址', '舊值', '新值'
        ]);
        
        // 數據行
        foreach ($logs as $log) {
            fputcsv($output, [
                $log['created_at'],
                $log['user_name'],
                $this->formatAction($log['action']),
                $log['table_name'],
                $log['record_id'],
                $log['ip_address'],
                $log['old_values'] ? json_encode($log['old_values'], JSON_UNESCAPED_UNICODE) : '',
                $log['new_values'] ? json_encode($log['new_values'], JSON_UNESCAPED_UNICODE) : ''
            ]);
        }
        
        fclose($output);
    }
}

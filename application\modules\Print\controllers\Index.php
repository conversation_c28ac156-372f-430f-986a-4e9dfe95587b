<?php
class PrintController extends Yaf_Controller_Abstract {
    
    /**
     * 列印工單確認單
     */
    public function workOrderConfirmAction() {
        $orderId = $this->getRequest()->getParam('order_id');
        $workOrder = $this->getWorkOrderData($orderId);
        
        // 取得範本
        $template = $this->getTemplate('work_order_confirm');
        
        // 替換範本變數
        $html = $this->replaceTemplateVars($template['template_content'], $workOrder);
        
        // 記錄列印
        $this->logPrint('work_order_confirm', $orderId, $template['id']);
        
        // 輸出PDF或直接列印
        $this->outputPrint($html, 'work_order_confirm_' . $workOrder['order_no']);
    }
    
    /**
     * 列印完工工單
     */
    public function workOrderCompleteAction() {
        $orderId = $this->getRequest()->getParam('order_id');
        $workOrder = $this->getWorkOrderData($orderId, true); // 包含零件明細
        
        $template = $this->getTemplate('work_order_complete');
        $html = $this->replaceTemplateVars($template['template_content'], $workOrder);
        
        $this->logPrint('work_order_complete', $orderId, $template['id']);
        $this->outputPrint($html, 'work_order_complete_' . $workOrder['order_no']);
    }
    
    /**
     * 列印發票
     */
    public function invoiceAction() {
        $invoiceId = $this->getRequest()->getParam('invoice_id');
        $invoice = $this->getInvoiceData($invoiceId);
        
        $template = $this->getTemplate('invoice');
        $html = $this->replaceTemplateVars($template['template_content'], $invoice);
        
        $this->logPrint('invoice', $invoiceId, $template['id']);
        $this->outputPrint($html, 'invoice_' . $invoice['invoice_no']);
    }
    
    private function getWorkOrderData($orderId, $includeParts = false) {
        // 取得工單完整資料
        $sql = "SELECT wo.*, c.name as customer_name, c.phone as customer_phone,
                       v.license_plate, v.brand, v.model, v.year,
                       u.full_name as technician_name,
                       t.name as garage_name, t.address as garage_address, t.phone as garage_phone
                FROM work_orders wo
                LEFT JOIN customers c ON wo.customer_id = c.id
                LEFT JOIN vehicles v ON wo.vehicle_id = v.id
                LEFT JOIN users u ON wo.technician_id = u.id
                LEFT JOIN tenants t ON wo.tenant_id = t.id
                WHERE wo.id = ? AND wo.tenant_id = ?";
        
        // 執行查詢...
        
        if ($includeParts) {
            // 取得零件明細
            $partsSql = "SELECT p.name, wop.quantity, wop.unit_price, wop.total_price
                        FROM work_order_parts wop
                        LEFT JOIN parts p ON wop.part_id = p.id
                        WHERE wop.work_order_id = ?";
            // 執行查詢並組合零件列表...
        }
        
        return $workOrderData;
    }
    
    private function outputPrint($html, $filename) {
        // 使用 TCPDF 或 DomPDF 生成PDF
        require_once APPLICATION_PATH . '/library/tcpdf/tcpdf.php';
        
        $pdf = new TCPDF();
        $pdf->AddPage();
        $pdf->writeHTML($html, true, false, true, false, '');
        
        // 直接輸出到瀏覽器列印
        $pdf->Output($filename . '.pdf', 'I');
    }
}
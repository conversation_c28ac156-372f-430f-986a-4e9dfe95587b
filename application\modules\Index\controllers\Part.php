<?php
/**
 * 零件管理控制器
 */
class PartController extends BaseController
{
    /**
     * 零件列表
     */
    public function indexAction()
    {
        if (!$this->hasPermission('part.view')) {
            return $this->error('權限不足', 403);
        }
        
        $page = $this->getParam('page', 1);
        $search = $this->getParam('search', '');
        $categoryId = $this->getParam('category_id', '');
        $lowStock = $this->getParam('low_stock', '');
        $perPage = 15;
        
        $partModel = new PartModel();
        $query = $partModel->query()
            ->select('p.*, pc.name as category_name')
            ->leftJoin('part_categories pc', 'p.category_id = pc.id');
        
        // 搜索條件
        if (!empty($search)) {
            $query->where("(p.part_no LIKE ? OR p.name LIKE ? OR p.brand LIKE ? OR p.specification LIKE ?)", 
                         "%{$search}%", "%{$search}%", "%{$search}%", "%{$search}%");
        }
        
        // 分類篩選
        if (!empty($categoryId)) {
            $query->where('p.category_id', $categoryId);
        }
        
        // 低庫存篩選
        if ($lowStock === '1') {
            $query->where('p.stock_quantity <= p.min_stock');
        }
        
        $query->orderBy('p.created_at', 'DESC');
        
        $result = $this->paginate($query, $page, $perPage);
        
        // 獲取統計數據
        $stats = $partModel->getStats();
        
        // 獲取分類列表
        $categoryModel = new PartCategoryModel();
        $categories = $categoryModel->findAll(['is_active' => 1], 'name ASC');
        
        $this->getView()->assign('parts', $result['data']);
        $this->getView()->assign('pagination', $result['pagination']);
        $this->getView()->assign('search', $search);
        $this->getView()->assign('categoryId', $categoryId);
        $this->getView()->assign('lowStock', $lowStock);
        $this->getView()->assign('categories', $categories);
        $this->getView()->assign('stats', $stats);
        $this->getView()->assign('title', '零件管理');
    }

    /**
     * 零件詳情
     */
    public function viewAction()
    {
        if (!$this->hasPermission('part.view')) {
            return $this->error('權限不足', 403);
        }
        
        $partId = $this->getParam('id');
        if (!$partId) {
            return $this->error('零件ID不能為空', 400);
        }
        
        $partModel = new PartModel();
        $part = $partModel->find($partId);
        
        if (!$part) {
            return $this->error('零件不存在', 404);
        }
        
        // 獲取使用記錄
        $usageHistory = $partModel->getUsageHistory($partId, 12);
        
        // 獲取分類資訊
        if ($part['category_id']) {
            $categoryModel = new PartCategoryModel();
            $category = $categoryModel->find($part['category_id']);
            $part['category'] = $category;
        }
        
        $this->getView()->assign('part', $part);
        $this->getView()->assign('usageHistory', $usageHistory);
        $this->getView()->assign('title', '零件詳情 - ' . $part['name']);
    }

    /**
     * 新增零件
     */
    public function addAction()
    {
        if (!$this->hasPermission('part.create')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleAddPart();
        }
        
        // 獲取分類列表
        $categoryModel = new PartCategoryModel();
        $categories = $categoryModel->getCategoryTree();
        
        $this->getView()->assign('categories', $categories);
        $this->getView()->assign('title', '新增零件');
    }

    /**
     * 處理新增零件
     */
    private function handleAddPart()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'part_no' => 'required|max:50',
            'name' => 'required|max:100',
            'brand' => 'max:50',
            'specification' => 'max:200',
            'unit' => 'required|max:20',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'min_stock' => 'integer|min:0',
            'max_stock' => 'integer|min:0',
            'location' => 'max:100',
            'supplier' => 'max:100',
            'notes' => 'max:500',
            'category_id' => 'integer'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        try {
            $partModel = new PartModel();
            
            // 檢查零件編號是否可用
            if (!$partModel->isPartNoAvailable($data['part_no'])) {
                throw new Exception('零件編號已存在');
            }
            
            $partData = [
                'category_id' => $data['category_id'] ?? null,
                'part_no' => $data['part_no'],
                'name' => $data['name'],
                'brand' => $data['brand'] ?? null,
                'specification' => $data['specification'] ?? null,
                'unit' => $data['unit'],
                'cost_price' => $data['cost_price'],
                'selling_price' => $data['selling_price'],
                'stock_quantity' => $data['stock_quantity'],
                'min_stock' => $data['min_stock'] ?? 0,
                'max_stock' => $data['max_stock'] ?? 0,
                'location' => $data['location'] ?? null,
                'supplier' => $data['supplier'] ?? null,
                'notes' => $data['notes'] ?? null
            ];
            
            $part = $partModel->create($partData);
            
            // 記錄操作日誌
            $this->logActivity('part_create', 'parts', $part['id'], null, $partData);
            
            if ($this->isAjax()) {
                return $this->success(['part_id' => $part['id']], '零件新增成功');
            }
            
            return $this->redirect('/part/view/' . $part['id'] . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('新增失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '新增失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 編輯零件
     */
    public function editAction()
    {
        if (!$this->hasPermission('part.edit')) {
            return $this->error('權限不足', 403);
        }
        
        $partId = $this->getParam('id');
        if (!$partId) {
            return $this->error('零件ID不能為空', 400);
        }
        
        $partModel = new PartModel();
        $part = $partModel->find($partId);
        
        if (!$part) {
            return $this->error('零件不存在', 404);
        }
        
        if ($this->isPost()) {
            return $this->handleEditPart($partId);
        }
        
        // 獲取分類列表
        $categoryModel = new PartCategoryModel();
        $categories = $categoryModel->getCategoryTree();
        
        $this->getView()->assign('part', $part);
        $this->getView()->assign('categories', $categories);
        $this->getView()->assign('title', '編輯零件 - ' . $part['name']);
    }

    /**
     * 處理編輯零件
     */
    private function handleEditPart($partId)
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'part_no' => 'required|max:50',
            'name' => 'required|max:100',
            'brand' => 'max:50',
            'specification' => 'max:200',
            'unit' => 'required|max:20',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'min_stock' => 'integer|min:0',
            'max_stock' => 'integer|min:0',
            'location' => 'max:100',
            'supplier' => 'max:100',
            'notes' => 'max:500',
            'category_id' => 'integer'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        try {
            $partModel = new PartModel();
            $oldData = $partModel->find($partId);
            
            // 檢查零件編號是否可用（排除當前零件）
            if (!$partModel->isPartNoAvailable($data['part_no'], $partId)) {
                throw new Exception('零件編號已存在');
            }
            
            $updateData = [
                'category_id' => $data['category_id'] ?? null,
                'part_no' => $data['part_no'],
                'name' => $data['name'],
                'brand' => $data['brand'] ?? null,
                'specification' => $data['specification'] ?? null,
                'unit' => $data['unit'],
                'cost_price' => $data['cost_price'],
                'selling_price' => $data['selling_price'],
                'min_stock' => $data['min_stock'] ?? 0,
                'max_stock' => $data['max_stock'] ?? 0,
                'location' => $data['location'] ?? null,
                'supplier' => $data['supplier'] ?? null,
                'notes' => $data['notes'] ?? null
            ];
            
            $partModel->update($partId, $updateData);
            
            // 記錄操作日誌
            $this->logActivity('part_update', 'parts', $partId, $oldData, $updateData);
            
            if ($this->isAjax()) {
                return $this->success(null, '零件更新成功');
            }
            
            return $this->redirect('/part/view/' . $partId . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 更新庫存
     */
    public function updateStockAction()
    {
        if (!$this->hasPermission('part.edit')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $partId = $this->getPost('part_id');
        $quantity = $this->getPost('quantity');
        $operation = $this->getPost('operation', 'add'); // add, subtract, set
        $notes = $this->getPost('notes', '');
        
        if (!$partId || !is_numeric($quantity)) {
            return $this->error('參數錯誤', 400);
        }
        
        if (!in_array($operation, ['add', 'subtract', 'set'])) {
            return $this->error('無效的操作類型', 400);
        }
        
        try {
            $partModel = new PartModel();
            $oldData = $partModel->find($partId);
            
            if (!$oldData) {
                return $this->error('零件不存在', 404);
            }
            
            $partModel->updateStock($partId, $quantity, $operation);
            
            // 記錄操作日誌
            $this->logActivity('part_stock_update', 'parts', $partId, 
                             ['old_stock' => $oldData['stock_quantity']], 
                             ['operation' => $operation, 'quantity' => $quantity, 'notes' => $notes]);
            
            return $this->success(null, '庫存更新成功');
            
        } catch (Exception $e) {
            return $this->error('庫存更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 搜索零件（AJAX）
     */
    public function searchAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        if (!$this->hasPermission('part.view')) {
            return $this->error('權限不足', 403);
        }
        
        $query = $this->getParam('q', '');
        $limit = $this->getParam('limit', 10);
        
        if (strlen($query) < 2) {
            return $this->success([]);
        }
        
        $partModel = new PartModel();
        $parts = $partModel->search($query, $limit);
        
        return $this->success($parts);
    }

    /**
     * 低庫存提醒
     */
    public function lowStockAction()
    {
        if (!$this->hasPermission('part.view')) {
            return $this->error('權限不足', 403);
        }
        
        $partModel = new PartModel();
        $lowStockParts = $partModel->getLowStockParts();
        
        $this->getView()->assign('parts', $lowStockParts);
        $this->getView()->assign('title', '低庫存提醒');
    }
}

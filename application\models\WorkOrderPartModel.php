<?php
/**
 * 工單零件模型
 */
class WorkOrderPartModel extends BaseModel
{
    protected $table = 'work_order_parts';
    protected $tenantField = null; // 通過 work_order 關聯租戶
    
    protected $fillable = [
        'work_order_id', 'part_id', 'quantity', 'unit_price', 'total_price', 'notes'
    ];

    /**
     * 獲取工單使用的零件
     */
    public function getOrderParts($workOrderId)
    {
        $sql = "
            SELECT wop.*, p.part_no, p.name as part_name, p.brand, p.unit, pc.name as category_name
            FROM work_order_parts wop
            LEFT JOIN parts p ON wop.part_id = p.id
            LEFT JOIN part_categories pc ON p.category_id = pc.id
            WHERE wop.work_order_id = ?
            ORDER BY wop.id ASC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$workOrderId]);
        
        return $stmt->fetchAll();
    }

    /**
     * 計算工單零件總費用
     */
    public function getOrderPartsCost($workOrderId)
    {
        $stmt = $this->db->prepare("
            SELECT COALESCE(SUM(total_price), 0) as total_cost
            FROM work_order_parts
            WHERE work_order_id = ?
        ");
        $stmt->execute([$workOrderId]);
        $result = $stmt->fetch();
        
        return $result['total_cost'];
    }

    /**
     * 添加零件到工單
     */
    public function addPartToOrder($workOrderId, $partId, $quantity, $unitPrice = null)
    {
        // 獲取零件資訊
        $partModel = new PartModel();
        $part = $partModel->find($partId);
        if (!$part) {
            throw new Exception('零件不存在');
        }
        
        // 檢查庫存
        if ($part['stock_quantity'] < $quantity) {
            throw new Exception('庫存不足');
        }
        
        // 使用零件售價作為預設單價
        if ($unitPrice === null) {
            $unitPrice = $part['selling_price'];
        }
        
        $totalPrice = $quantity * $unitPrice;
        
        $this->beginTransaction();
        
        try {
            // 添加零件記錄
            $data = [
                'work_order_id' => $workOrderId,
                'part_id' => $partId,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice
            ];
            
            $result = $this->create($data);
            
            // 更新庫存
            $partModel->updateStock($partId, $quantity, 'subtract');
            
            $this->commit();
            return $result;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 從工單移除零件
     */
    public function removePartFromOrder($workOrderPartId)
    {
        $orderPart = $this->find($workOrderPartId);
        if (!$orderPart) {
            return false;
        }
        
        $this->beginTransaction();
        
        try {
            // 恢復庫存
            $partModel = new PartModel();
            $partModel->updateStock($orderPart['part_id'], $orderPart['quantity'], 'add');
            
            // 刪除記錄
            $this->delete($workOrderPartId);
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 更新工單零件
     */
    public function updateOrderPart($workOrderPartId, $quantity, $unitPrice)
    {
        $orderPart = $this->find($workOrderPartId);
        if (!$orderPart) {
            return false;
        }
        
        $oldQuantity = $orderPart['quantity'];
        $quantityDiff = $quantity - $oldQuantity;
        
        $this->beginTransaction();
        
        try {
            // 檢查庫存（如果增加數量）
            if ($quantityDiff > 0) {
                $partModel = new PartModel();
                $part = $partModel->find($orderPart['part_id']);
                if ($part['stock_quantity'] < $quantityDiff) {
                    throw new Exception('庫存不足');
                }
            }
            
            // 更新零件記錄
            $totalPrice = $quantity * $unitPrice;
            $this->update($workOrderPartId, [
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice
            ]);
            
            // 更新庫存
            if ($quantityDiff != 0) {
                $partModel = new PartModel();
                $operation = $quantityDiff > 0 ? 'subtract' : 'add';
                $partModel->updateStock($orderPart['part_id'], abs($quantityDiff), $operation);
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 批量添加零件到工單
     */
    public function batchAddPartsToOrder($workOrderId, $parts)
    {
        $this->beginTransaction();
        
        try {
            foreach ($parts as $partData) {
                $this->addPartToOrder(
                    $workOrderId,
                    $partData['part_id'],
                    $partData['quantity'],
                    $partData['unit_price'] ?? null
                );
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 清空工單所有零件
     */
    public function clearOrderParts($workOrderId)
    {
        $orderParts = $this->getOrderParts($workOrderId);
        
        $this->beginTransaction();
        
        try {
            $partModel = new PartModel();
            
            foreach ($orderParts as $orderPart) {
                // 恢復庫存
                $partModel->updateStock($orderPart['part_id'], $orderPart['quantity'], 'add');
            }
            
            // 刪除所有零件記錄
            $stmt = $this->db->prepare("DELETE FROM work_order_parts WHERE work_order_id = ?");
            $stmt->execute([$workOrderId]);
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 獲取零件使用統計
     */
    public function getPartUsageStats($partId, $months = 12)
    {
        $stmt = $this->db->prepare("
            SELECT 
                DATE_FORMAT(wo.created_at, '%Y-%m') as month,
                SUM(wop.quantity) as total_quantity,
                SUM(wop.total_price) as total_amount,
                COUNT(DISTINCT wo.id) as order_count
            FROM work_order_parts wop
            JOIN work_orders wo ON wop.work_order_id = wo.id
            WHERE wop.part_id = ?
            AND wo.created_at >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            GROUP BY DATE_FORMAT(wo.created_at, '%Y-%m')
            ORDER BY month DESC
        ");
        $stmt->execute([$partId, $months]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取工單零件成本分析
     */
    public function getOrderCostAnalysis($workOrderId)
    {
        $sql = "
            SELECT 
                wop.*,
                p.part_no, p.name as part_name, p.cost_price,
                (wop.unit_price - p.cost_price) as profit_per_unit,
                (wop.total_price - (p.cost_price * wop.quantity)) as total_profit
            FROM work_order_parts wop
            LEFT JOIN parts p ON wop.part_id = p.id
            WHERE wop.work_order_id = ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$workOrderId]);
        
        return $stmt->fetchAll();
    }

    /**
     * 複製零件到另一個工單
     */
    public function copyPartsToOrder($fromOrderId, $toOrderId)
    {
        $sourceParts = $this->getOrderParts($fromOrderId);
        
        $this->beginTransaction();
        
        try {
            foreach ($sourceParts as $part) {
                $this->addPartToOrder(
                    $toOrderId,
                    $part['part_id'],
                    $part['quantity'],
                    $part['unit_price']
                );
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
}

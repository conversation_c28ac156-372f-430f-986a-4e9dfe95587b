<?php
/**
 * 系統啟動類別
 * 負責初始化系統核心組件
 */
class Bootstrap extends Yaf_Bootstrap_Abstract
{
    /**
     * 初始化配置
     */
    public function _initConfig()
    {
        $config = Yaf_Application::app()->getConfig();
        Yaf_Registry::set('config', $config);
    }

    /**
     * 初始化資料庫連接
     */
    public function _initDatabase()
    {
        $config = Yaf_Registry::get('config');
        $dbConfig = $config->database;
        
        try {
            $dsn = "mysql:host={$dbConfig->host};port={$dbConfig->port};dbname={$dbConfig->dbname};charset={$dbConfig->charset}";
            $pdo = new PDO($dsn, $dbConfig->username, $dbConfig->password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$dbConfig->charset}"
            ]);
            
            Yaf_Registry::set('db', $pdo);
        } catch (PDOException $e) {
            throw new Exception('資料庫連接失敗: ' . $e->getMessage());
        }
    }

    /**
     * 初始化 Redis 連接
     */
    public function _initRedis()
    {
        $config = Yaf_Registry::get('config');
        $redisConfig = $config->redis;
        
        if (class_exists('Redis')) {
            try {
                $redis = new Redis();
                $redis->connect($redisConfig->host, $redisConfig->port);
                $redis->select($redisConfig->database);
                Yaf_Registry::set('redis', $redis);
            } catch (Exception $e) {
                // Redis 連接失敗時使用文件 session
                error_log('Redis 連接失敗: ' . $e->getMessage());
            }
        }
    }

    /**
     * 初始化 Session
     */
    public function _initSession()
    {
        $config = Yaf_Registry::get('config');
        
        // 設定 session 配置
        ini_set('session.gc_maxlifetime', $config->system->session_lifetime);
        ini_set('session.cookie_lifetime', $config->system->session_lifetime);
        
        // 如果有 Redis，使用 Redis 存儲 session
        $redis = Yaf_Registry::get('redis');
        if ($redis) {
            ini_set('session.save_handler', 'redis');
            ini_set('session.save_path', "tcp://{$config->redis->host}:{$config->redis->port}");
        }
        
        session_start();
    }

    /**
     * 初始化時區
     */
    public function _initTimezone()
    {
        $config = Yaf_Registry::get('config');
        date_default_timezone_set($config->system->timezone);
    }

    /**
     * 初始化錯誤處理
     */
    public function _initErrorHandler()
    {
        // 設定錯誤處理
        set_error_handler([$this, 'errorHandler']);
        set_exception_handler([$this, 'exceptionHandler']);
    }

    /**
     * 初始化多租戶中間件
     */
    public function _initTenant()
    {
        // 註冊多租戶插件
        $dispatcher = Yaf_Dispatcher::getInstance();
        $dispatcher->registerPlugin(new TenantPlugin());
    }

    /**
     * 初始化路由
     */
    public function _initRoute()
    {
        $router = Yaf_Dispatcher::getInstance()->getRouter();
        
        // API 路由
        $apiRoute = new Yaf_Route_Regex(
            '#^/api/([a-zA-Z]+)/([a-zA-Z]+)/?(.*)$#',
            ['module' => 'Index', 'controller' => 'Api', 'action' => 'index'],
            ['1' => 'resource', '2' => 'method', '3' => 'params']
        );
        $router->addRoute('api', $apiRoute);
        
        // 租戶子域名路由
        $tenantRoute = new Yaf_Route_Regex(
            '#^/tenant/([a-zA-Z0-9]+)/?(.*)$#',
            ['module' => 'Tenant', 'controller' => 'Index', 'action' => 'index'],
            ['1' => 'subdomain', '2' => 'path']
        );
        $router->addRoute('tenant', $tenantRoute);
    }

    /**
     * 錯誤處理器
     */
    public function errorHandler($errno, $errstr, $errfile, $errline)
    {
        $error = [
            'type' => 'error',
            'errno' => $errno,
            'message' => $errstr,
            'file' => $errfile,
            'line' => $errline,
            'time' => date('Y-m-d H:i:s')
        ];
        
        error_log(json_encode($error, JSON_UNESCAPED_UNICODE));
        
        // 在開發環境顯示錯誤
        if (APPLICATION_ENV === 'development') {
            echo "<pre>" . print_r($error, true) . "</pre>";
        }
    }

    /**
     * 異常處理器
     */
    public function exceptionHandler($exception)
    {
        $error = [
            'type' => 'exception',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'time' => date('Y-m-d H:i:s')
        ];
        
        error_log(json_encode($error, JSON_UNESCAPED_UNICODE));
        
        // 在開發環境顯示異常
        if (APPLICATION_ENV === 'development') {
            echo "<pre>" . print_r($error, true) . "</pre>";
        } else {
            // 生產環境顯示友好錯誤頁面
            header('HTTP/1.1 500 Internal Server Error');
            echo '系統發生錯誤，請稍後再試。';
        }
    }
}

[common]
; 應用程式設定
application.directory = APPLICATION_PATH
application.dispatcher.defaultModule = "Index"
application.dispatcher.defaultController = "Index"
application.dispatcher.defaultAction = "index"

; 模組設定
application.modules = "Index,Auth,Tenant,Payment,Admin"

; 視圖設定
application.view.ext = "phtml"

; 資料庫設定 (XAMPP 預設)
database.adapter = "PDO_MYSQL"
database.params.host = "localhost"
database.params.port = 3306
database.params.username = "root"
database.params.password = ""
database.params.dbname = "car_maintenance"
database.params.charset = "utf8mb4"

; Session 設定
session.name = "CAR_MAINTENANCE_SESSION"
session.lifetime = 7200
session.cookie_httponly = 1
session.cookie_secure = 0
session.use_strict_mode = 1

; 安全設定
security.salt = "your_random_salt_here_change_this"
security.csrf_token_name = "_token"

; 郵件設定 (開發環境)
mail.smtp_host = ""
mail.smtp_port = 587
mail.smtp_username = ""
mail.smtp_password = ""
mail.from_email = "<EMAIL>"
mail.from_name = "汽車保養廠管理系統"

; 支付設定 (測試環境)
payment.ecpay.merchant_id = "2000132"
payment.ecpay.hash_key = "5294y06JbISpM5x9"
payment.ecpay.hash_iv = "v77hoKGq4kWxNNIS"
payment.ecpay.test_mode = 1

; 檔案上傳設定
upload.max_size = 52428800
upload.allowed_types = "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx"
upload.path = APPLICATION_PATH "/../public/uploads/"

; 快取設定
cache.type = "file"
cache.path = APPLICATION_PATH "/storage/cache/"

; 日誌設定
log.level = "DEBUG"
log.path = APPLICATION_PATH "/storage/logs/"

; 多租戶設定
tenant.default_subdomain = "demo"
tenant.trial_days = 14

[development : common]
; 開發環境設定
application.debug = 1
application.showErrors = 1
database.params.persistent = 0

; 錯誤報告
error_reporting = E_ALL
display_errors = 1
log_errors = 1

[testing : common]
; 測試環境設定
application.debug = 1
application.showErrors = 1
database.params.dbname = "car_maintenance_test"

[production : common]
; 生產環境設定
application.debug = 0
application.showErrors = 0
database.params.persistent = 1

; 安全設定
session.cookie_secure = 1
session.cookie_samesite = "Strict"

; 效能設定
opcache.enable = 1
opcache.memory_consumption = 128
opcache.max_accelerated_files = 4000

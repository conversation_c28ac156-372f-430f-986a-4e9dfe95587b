<?php
/**
 * 查詢建構器
 * 提供流暢的 SQL 查詢建構介面
 */
class QueryBuilder
{
    private $db;
    private $table;
    private $tenantField;
    private $select = ['*'];
    private $joins = [];
    private $where = [];
    private $params = [];
    private $orderBy = [];
    private $groupBy = [];
    private $having = [];
    private $limit = null;
    private $offset = null;

    public function __construct($db, $table, $tenantField = null)
    {
        $this->db = $db;
        $this->table = $table;
        $this->tenantField = $tenantField;
        
        // 自動添加租戶條件
        if ($this->tenantField) {
            $tenant = Yaf_Registry::get('current_tenant');
            if ($tenant) {
                $this->where($this->tenantField, $tenant['id']);
            }
        }
    }

    /**
     * 選擇字段
     */
    public function select($fields)
    {
        if (is_string($fields)) {
            $this->select = [$fields];
        } elseif (is_array($fields)) {
            $this->select = $fields;
        }
        return $this;
    }

    /**
     * JOIN 查詢
     */
    public function join($table, $first, $operator = null, $second = null, $type = 'INNER')
    {
        if ($operator === null) {
            // 假設 $first 是完整的 ON 條件
            $condition = $first;
        } else {
            $condition = "{$first} {$operator} {$second}";
        }
        
        $this->joins[] = "{$type} JOIN {$table} ON {$condition}";
        return $this;
    }

    /**
     * LEFT JOIN
     */
    public function leftJoin($table, $first, $operator = null, $second = null)
    {
        return $this->join($table, $first, $operator, $second, 'LEFT');
    }

    /**
     * RIGHT JOIN
     */
    public function rightJoin($table, $first, $operator = null, $second = null)
    {
        return $this->join($table, $first, $operator, $second, 'RIGHT');
    }

    /**
     * WHERE 條件
     */
    public function where($field, $operator = null, $value = null)
    {
        if ($operator === null) {
            // 假設 $field 是完整的條件
            $this->where[] = $field;
        } elseif ($value === null) {
            // $operator 實際上是值
            $this->where[] = "{$field} = ?";
            $this->params[] = $operator;
        } else {
            $this->where[] = "{$field} {$operator} ?";
            $this->params[] = $value;
        }
        return $this;
    }

    /**
     * WHERE IN 條件
     */
    public function whereIn($field, $values)
    {
        if (!is_array($values) || empty($values)) {
            return $this;
        }
        
        $placeholders = str_repeat('?,', count($values) - 1) . '?';
        $this->where[] = "{$field} IN ({$placeholders})";
        $this->params = array_merge($this->params, $values);
        return $this;
    }

    /**
     * WHERE NOT IN 條件
     */
    public function whereNotIn($field, $values)
    {
        if (!is_array($values) || empty($values)) {
            return $this;
        }
        
        $placeholders = str_repeat('?,', count($values) - 1) . '?';
        $this->where[] = "{$field} NOT IN ({$placeholders})";
        $this->params = array_merge($this->params, $values);
        return $this;
    }

    /**
     * WHERE LIKE 條件
     */
    public function whereLike($field, $value)
    {
        $this->where[] = "{$field} LIKE ?";
        $this->params[] = $value;
        return $this;
    }

    /**
     * WHERE BETWEEN 條件
     */
    public function whereBetween($field, $min, $max)
    {
        $this->where[] = "{$field} BETWEEN ? AND ?";
        $this->params[] = $min;
        $this->params[] = $max;
        return $this;
    }

    /**
     * WHERE NULL 條件
     */
    public function whereNull($field)
    {
        $this->where[] = "{$field} IS NULL";
        return $this;
    }

    /**
     * WHERE NOT NULL 條件
     */
    public function whereNotNull($field)
    {
        $this->where[] = "{$field} IS NOT NULL";
        return $this;
    }

    /**
     * OR WHERE 條件
     */
    public function orWhere($field, $operator = null, $value = null)
    {
        if (empty($this->where)) {
            return $this->where($field, $operator, $value);
        }
        
        if ($operator === null) {
            $this->where[] = "OR {$field}";
        } elseif ($value === null) {
            $this->where[] = "OR {$field} = ?";
            $this->params[] = $operator;
        } else {
            $this->where[] = "OR {$field} {$operator} ?";
            $this->params[] = $value;
        }
        return $this;
    }

    /**
     * ORDER BY
     */
    public function orderBy($field, $direction = 'ASC')
    {
        $this->orderBy[] = "{$field} {$direction}";
        return $this;
    }

    /**
     * GROUP BY
     */
    public function groupBy($field)
    {
        $this->groupBy[] = $field;
        return $this;
    }

    /**
     * HAVING
     */
    public function having($field, $operator, $value)
    {
        $this->having[] = "{$field} {$operator} ?";
        $this->params[] = $value;
        return $this;
    }

    /**
     * LIMIT
     */
    public function limit($limit)
    {
        $this->limit = $limit;
        return $this;
    }

    /**
     * OFFSET
     */
    public function offset($offset)
    {
        $this->offset = $offset;
        return $this;
    }

    /**
     * 分頁
     */
    public function paginate($page, $perPage = 15)
    {
        $this->limit = $perPage;
        $this->offset = ($page - 1) * $perPage;
        return $this;
    }

    /**
     * 建構 SQL 查詢
     */
    private function buildQuery()
    {
        $sql = "SELECT " . implode(', ', $this->select) . " FROM {$this->table}";
        
        // 添加 JOIN
        if (!empty($this->joins)) {
            $sql .= " " . implode(' ', $this->joins);
        }
        
        // 添加 WHERE
        if (!empty($this->where)) {
            $sql .= " WHERE " . implode(' AND ', $this->where);
        }
        
        // 添加 GROUP BY
        if (!empty($this->groupBy)) {
            $sql .= " GROUP BY " . implode(', ', $this->groupBy);
        }
        
        // 添加 HAVING
        if (!empty($this->having)) {
            $sql .= " HAVING " . implode(' AND ', $this->having);
        }
        
        // 添加 ORDER BY
        if (!empty($this->orderBy)) {
            $sql .= " ORDER BY " . implode(', ', $this->orderBy);
        }
        
        // 添加 LIMIT 和 OFFSET
        if ($this->limit !== null) {
            $sql .= " LIMIT {$this->limit}";
            if ($this->offset !== null) {
                $sql .= " OFFSET {$this->offset}";
            }
        }
        
        return $sql;
    }

    /**
     * 執行查詢並返回所有結果
     */
    public function get()
    {
        $sql = $this->buildQuery();
        $stmt = $this->db->prepare($sql);
        $stmt->execute($this->params);
        return $stmt->fetchAll();
    }

    /**
     * 執行查詢並返回第一個結果
     */
    public function first()
    {
        $this->limit(1);
        $results = $this->get();
        return !empty($results) ? $results[0] : null;
    }

    /**
     * 計算記錄數量
     */
    public function count()
    {
        $originalSelect = $this->select;
        $this->select = ['COUNT(*) as count'];
        
        $sql = $this->buildQuery();
        $stmt = $this->db->prepare($sql);
        $stmt->execute($this->params);
        $result = $stmt->fetch();
        
        $this->select = $originalSelect;
        return (int)$result['count'];
    }

    /**
     * 檢查記錄是否存在
     */
    public function exists()
    {
        return $this->count() > 0;
    }

    /**
     * 獲取原始 SQL 查詢（用於調試）
     */
    public function toSql()
    {
        return $this->buildQuery();
    }

    /**
     * 獲取查詢參數（用於調試）
     */
    public function getParams()
    {
        return $this->params;
    }
}

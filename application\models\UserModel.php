<?php
/**
 * 用戶模型
 */
class UserModel extends BaseModel
{
    protected $table = 'users';
    
    protected $fillable = [
        'username', 'email', 'password', 'full_name', 'phone', 'role', 'is_active'
    ];
    
    protected $hidden = ['password'];

    /**
     * 根據用戶名或郵箱查找用戶
     */
    public function findByLogin($login)
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return null;
        }
        
        $stmt = $this->db->prepare("
            SELECT * FROM users 
            WHERE tenant_id = ? 
            AND (username = ? OR email = ?) 
            AND is_active = 1
        ");
        $stmt->execute([$tenantId, $login, $login]);
        return $stmt->fetch();
    }

    /**
     * 驗證用戶登入
     */
    public function authenticate($login, $password)
    {
        $user = $this->findByLogin($login);
        if (!$user) {
            return false;
        }
        
        if (password_verify($password, $user['password'])) {
            // 更新最後登入時間
            $this->update($user['id'], ['last_login_at' => date('Y-m-d H:i:s')]);
            return $this->hideFields($user);
        }
        
        return false;
    }

    /**
     * 創建用戶
     */
    public function create($data)
    {
        // 加密密碼
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return parent::create($data);
    }

    /**
     * 更新用戶
     */
    public function update($id, $data)
    {
        // 如果更新密碼，需要加密
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return parent::update($id, $data);
    }

    /**
     * 檢查用戶名是否可用
     */
    public function isUsernameAvailable($username, $excludeId = null)
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return false;
        }
        
        $sql = "SELECT COUNT(*) as count FROM users WHERE tenant_id = ? AND username = ?";
        $params = [$tenantId, $username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] == 0;
    }

    /**
     * 檢查郵箱是否可用
     */
    public function isEmailAvailable($email, $excludeId = null)
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return false;
        }
        
        $sql = "SELECT COUNT(*) as count FROM users WHERE tenant_id = ? AND email = ?";
        $params = [$tenantId, $email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] == 0;
    }

    /**
     * 獲取技師列表
     */
    public function getTechnicians()
    {
        return $this->findAll(['role' => 'technician', 'is_active' => 1], 'full_name ASC');
    }

    /**
     * 獲取用戶統計
     */
    public function getStats()
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stats = [];
        
        // 按角色統計
        $stmt = $this->db->prepare("
            SELECT role, COUNT(*) as count 
            FROM users 
            WHERE tenant_id = ? AND is_active = 1 
            GROUP BY role
        ");
        $stmt->execute([$tenantId]);
        $roleStats = $stmt->fetchAll();
        
        foreach ($roleStats as $stat) {
            $stats['by_role'][$stat['role']] = $stat['count'];
        }
        
        // 總用戶數
        $stats['total'] = array_sum($stats['by_role'] ?? []);
        
        // 本月新增用戶
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM users 
            WHERE tenant_id = ? 
            AND YEAR(created_at) = YEAR(NOW()) 
            AND MONTH(created_at) = MONTH(NOW())
        ");
        $stmt->execute([$tenantId]);
        $stats['monthly_new'] = $stmt->fetch()['count'];
        
        // 最近登入用戶
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM users 
            WHERE tenant_id = ? 
            AND last_login_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");
        $stmt->execute([$tenantId]);
        $stats['recent_active'] = $stmt->fetch()['count'];
        
        return $stats;
    }

    /**
     * 重設密碼
     */
    public function resetPassword($userId, $newPassword)
    {
        return $this->update($userId, ['password' => $newPassword]);
    }

    /**
     * 停用用戶
     */
    public function deactivate($userId)
    {
        return $this->update($userId, ['is_active' => 0]);
    }

    /**
     * 啟用用戶
     */
    public function activate($userId)
    {
        return $this->update($userId, ['is_active' => 1]);
    }

    /**
     * 獲取用戶權限
     */
    public function getUserPermissions($userId)
    {
        $user = $this->find($userId);
        if (!$user) {
            return [];
        }
        
        $permissions = [
            'owner' => ['*'],
            'admin' => [
                'user.view', 'user.create', 'user.edit', 'user.delete',
                'customer.view', 'customer.create', 'customer.edit', 'customer.delete',
                'vehicle.view', 'vehicle.create', 'vehicle.edit', 'vehicle.delete',
                'workorder.view', 'workorder.create', 'workorder.edit', 'workorder.delete',
                'part.view', 'part.create', 'part.edit', 'part.delete',
                'appointment.view', 'appointment.create', 'appointment.edit', 'appointment.delete',
                'payment.view', 'payment.create',
                'report.view', 'setting.view', 'setting.edit'
            ],
            'manager' => [
                'customer.view', 'customer.create', 'customer.edit',
                'vehicle.view', 'vehicle.create', 'vehicle.edit',
                'workorder.view', 'workorder.create', 'workorder.edit',
                'part.view', 'part.create', 'part.edit',
                'appointment.view', 'appointment.create', 'appointment.edit',
                'payment.view', 'payment.create',
                'report.view'
            ],
            'technician' => [
                'customer.view',
                'vehicle.view',
                'workorder.view', 'workorder.edit',
                'part.view',
                'appointment.view'
            ],
            'staff' => [
                'customer.view',
                'vehicle.view',
                'workorder.view',
                'appointment.view', 'appointment.create', 'appointment.edit'
            ]
        ];
        
        return $permissions[$user['role']] ?? [];
    }

    /**
     * 檢查用戶是否有權限
     */
    public function hasPermission($userId, $permission)
    {
        $userPermissions = $this->getUserPermissions($userId);
        return in_array('*', $userPermissions) || in_array($permission, $userPermissions);
    }
}

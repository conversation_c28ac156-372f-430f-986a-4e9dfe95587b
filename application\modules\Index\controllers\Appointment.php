<?php
/**
 * 預約管理控制器
 */
class AppointmentController extends BaseController
{
    /**
     * 預約列表
     */
    public function indexAction()
    {
        if (!$this->hasPermission('appointment.view')) {
            return $this->error('權限不足', 403);
        }
        
        $page = $this->getParam('page', 1);
        $search = $this->getParam('search', '');
        $status = $this->getParam('status', '');
        $date = $this->getParam('date', '');
        $technicianId = $this->getParam('technician_id', '');
        $perPage = 15;
        
        $appointmentModel = new AppointmentModel();
        $query = $appointmentModel->query()
            ->select('a.*, c.name as customer_name, c.phone as customer_phone, v.license_plate, u.full_name as technician_name')
            ->leftJoin('customers c', 'a.customer_id = c.id')
            ->leftJoin('vehicles v', 'a.vehicle_id = v.id')
            ->leftJoin('users u', 'a.technician_id = u.id');
        
        // 搜索條件
        if (!empty($search)) {
            $query->where("(a.appointment_no LIKE ? OR c.name LIKE ? OR v.license_plate LIKE ? OR a.description LIKE ?)", 
                         "%{$search}%", "%{$search}%", "%{$search}%", "%{$search}%");
        }
        
        // 狀態篩選
        if (!empty($status)) {
            $query->where('a.status', $status);
        }
        
        // 日期篩選
        if (!empty($date)) {
            $query->where('DATE(a.appointment_date)', $date);
        }
        
        // 技師篩選
        if (!empty($technicianId)) {
            $query->where('a.technician_id', $technicianId);
        }
        
        $query->orderBy('a.appointment_date', 'ASC')
              ->orderBy('a.appointment_time', 'ASC');
        
        $result = $this->paginate($query, $page, $perPage);
        
        // 獲取統計數據
        $stats = $appointmentModel->getStats();
        
        // 獲取技師列表
        $userModel = new UserModel();
        $technicians = $userModel->getTechnicians();
        
        $this->getView()->assign('appointments', $result['data']);
        $this->getView()->assign('pagination', $result['pagination']);
        $this->getView()->assign('search', $search);
        $this->getView()->assign('status', $status);
        $this->getView()->assign('date', $date);
        $this->getView()->assign('technicianId', $technicianId);
        $this->getView()->assign('technicians', $technicians);
        $this->getView()->assign('stats', $stats);
        $this->getView()->assign('title', '預約管理');
    }

    /**
     * 預約詳情
     */
    public function viewAction()
    {
        if (!$this->hasPermission('appointment.view')) {
            return $this->error('權限不足', 403);
        }
        
        $appointmentId = $this->getParam('id');
        if (!$appointmentId) {
            return $this->error('預約ID不能為空', 400);
        }
        
        $appointmentModel = new AppointmentModel();
        $appointment = $appointmentModel->find($appointmentId);
        
        if (!$appointment) {
            return $this->error('預約不存在', 404);
        }
        
        // 獲取關聯資料
        $customerModel = new CustomerModel();
        $customer = $customerModel->find($appointment['customer_id']);
        
        $vehicleModel = new VehicleModel();
        $vehicle = $vehicleModel->find($appointment['vehicle_id']);
        
        $userModel = new UserModel();
        $technician = $appointment['technician_id'] ? $userModel->find($appointment['technician_id']) : null;
        
        $this->getView()->assign('appointment', $appointment);
        $this->getView()->assign('customer', $customer);
        $this->getView()->assign('vehicle', $vehicle);
        $this->getView()->assign('technician', $technician);
        $this->getView()->assign('title', '預約詳情 - ' . $appointment['appointment_no']);
    }

    /**
     * 新增預約
     */
    public function addAction()
    {
        if (!$this->hasPermission('appointment.create')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleAddAppointment();
        }
        
        // 獲取客戶列表
        $customerModel = new CustomerModel();
        $customers = $customerModel->findAll(['is_active' => 1], 'name ASC');
        
        // 獲取技師列表
        $userModel = new UserModel();
        $technicians = $userModel->getTechnicians();
        
        // 預設值
        $defaultCustomerId = $this->getParam('customer_id');
        $defaultVehicleId = $this->getParam('vehicle_id');
        $defaultDate = $this->getParam('date', date('Y-m-d'));
        
        $this->getView()->assign('customers', $customers);
        $this->getView()->assign('technicians', $technicians);
        $this->getView()->assign('defaultCustomerId', $defaultCustomerId);
        $this->getView()->assign('defaultVehicleId', $defaultVehicleId);
        $this->getView()->assign('defaultDate', $defaultDate);
        $this->getView()->assign('title', '新增預約');
    }

    /**
     * 處理新增預約
     */
    private function handleAddAppointment()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'customer_id' => 'required|integer',
            'vehicle_id' => 'required|integer',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required|time',
            'estimated_duration' => 'required|integer|min:15|max:480',
            'service_type' => 'required|max:100',
            'description' => 'max:500',
            'customer_notes' => 'max:500',
            'technician_id' => 'integer'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        // 檢查預約日期不能是過去
        if (strtotime($data['appointment_date']) < strtotime(date('Y-m-d'))) {
            if ($this->isAjax()) {
                return $this->error('預約日期不能是過去的日期');
            }
            $this->getView()->assign('error', '預約日期不能是過去的日期');
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        try {
            // 檢查客戶和車輛是否存在
            $customerModel = new CustomerModel();
            $customer = $customerModel->find($data['customer_id']);
            if (!$customer) {
                throw new Exception('客戶不存在');
            }
            
            $vehicleModel = new VehicleModel();
            $vehicle = $vehicleModel->find($data['vehicle_id']);
            if (!$vehicle) {
                throw new Exception('車輛不存在');
            }
            
            // 檢查車輛是否屬於該客戶
            if ($vehicle['customer_id'] != $data['customer_id']) {
                throw new Exception('車輛不屬於該客戶');
            }
            
            $appointmentModel = new AppointmentModel();
            
            // 檢查時間衝突
            if ($appointmentModel->checkTimeConflict(
                $data['appointment_date'], 
                $data['appointment_time'], 
                $data['estimated_duration'], 
                $data['technician_id'] ?? null
            )) {
                throw new Exception('該時間段已有預約，請選擇其他時間');
            }
            
            $appointmentData = [
                'customer_id' => $data['customer_id'],
                'vehicle_id' => $data['vehicle_id'],
                'technician_id' => $data['technician_id'] ?? null,
                'appointment_date' => $data['appointment_date'],
                'appointment_time' => $data['appointment_time'],
                'estimated_duration' => $data['estimated_duration'],
                'service_type' => $data['service_type'],
                'description' => $data['description'] ?? null,
                'customer_notes' => $data['customer_notes'] ?? null,
                'status' => 'confirmed'
            ];
            
            $appointment = $appointmentModel->create($appointmentData);
            
            // 記錄操作日誌
            $this->logActivity('appointment_create', 'appointments', $appointment['id'], null, $appointmentData);
            
            if ($this->isAjax()) {
                return $this->success(['appointment_id' => $appointment['id']], '預約新增成功');
            }
            
            return $this->redirect('/appointment/view/' . $appointment['id'] . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('新增失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '新增失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 編輯預約
     */
    public function editAction()
    {
        if (!$this->hasPermission('appointment.edit')) {
            return $this->error('權限不足', 403);
        }
        
        $appointmentId = $this->getParam('id');
        if (!$appointmentId) {
            return $this->error('預約ID不能為空', 400);
        }
        
        $appointmentModel = new AppointmentModel();
        $appointment = $appointmentModel->find($appointmentId);
        
        if (!$appointment) {
            return $this->error('預約不存在', 404);
        }
        
        if ($this->isPost()) {
            return $this->handleEditAppointment($appointmentId);
        }
        
        // 獲取客戶列表
        $customerModel = new CustomerModel();
        $customers = $customerModel->findAll(['is_active' => 1], 'name ASC');
        
        // 獲取技師列表
        $userModel = new UserModel();
        $technicians = $userModel->getTechnicians();
        
        $this->getView()->assign('appointment', $appointment);
        $this->getView()->assign('customers', $customers);
        $this->getView()->assign('technicians', $technicians);
        $this->getView()->assign('title', '編輯預約 - ' . $appointment['appointment_no']);
    }

    /**
     * 處理編輯預約
     */
    private function handleEditAppointment($appointmentId)
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'customer_id' => 'required|integer',
            'vehicle_id' => 'required|integer',
            'appointment_date' => 'required|date',
            'appointment_time' => 'required|time',
            'estimated_duration' => 'required|integer|min:15|max:480',
            'service_type' => 'required|max:100',
            'description' => 'max:500',
            'customer_notes' => 'max:500',
            'technician_id' => 'integer'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        try {
            $appointmentModel = new AppointmentModel();
            $oldData = $appointmentModel->find($appointmentId);
            
            // 檢查時間衝突（排除當前預約）
            if ($appointmentModel->checkTimeConflict(
                $data['appointment_date'], 
                $data['appointment_time'], 
                $data['estimated_duration'], 
                $data['technician_id'] ?? null,
                $appointmentId
            )) {
                throw new Exception('該時間段已有預約，請選擇其他時間');
            }
            
            $updateData = [
                'customer_id' => $data['customer_id'],
                'vehicle_id' => $data['vehicle_id'],
                'technician_id' => $data['technician_id'] ?? null,
                'appointment_date' => $data['appointment_date'],
                'appointment_time' => $data['appointment_time'],
                'estimated_duration' => $data['estimated_duration'],
                'service_type' => $data['service_type'],
                'description' => $data['description'] ?? null,
                'customer_notes' => $data['customer_notes'] ?? null
            ];
            
            $appointmentModel->update($appointmentId, $updateData);
            
            // 記錄操作日誌
            $this->logActivity('appointment_update', 'appointments', $appointmentId, $oldData, $updateData);
            
            if ($this->isAjax()) {
                return $this->success(null, '預約更新成功');
            }
            
            return $this->redirect('/appointment/view/' . $appointmentId . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 取消預約
     */
    public function cancelAction()
    {
        if (!$this->hasPermission('appointment.edit')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $appointmentId = $this->getPost('appointment_id');
        $reason = $this->getPost('reason', '');
        
        if (!$appointmentId) {
            return $this->error('預約ID不能為空', 400);
        }
        
        try {
            $appointmentModel = new AppointmentModel();
            $appointment = $appointmentModel->find($appointmentId);
            
            if (!$appointment) {
                return $this->error('預約不存在', 404);
            }
            
            if ($appointment['status'] === 'cancelled') {
                return $this->error('預約已取消');
            }
            
            $appointmentModel->cancelAppointment($appointmentId, $reason);
            
            // 記錄操作日誌
            $this->logActivity('appointment_cancel', 'appointments', $appointmentId, 
                             ['old_status' => $appointment['status']], 
                             ['new_status' => 'cancelled', 'reason' => $reason]);
            
            return $this->success(null, '預約已取消');
            
        } catch (Exception $e) {
            return $this->error('取消預約失敗：' . $e->getMessage());
        }
    }

    /**
     * 完成預約
     */
    public function completeAction()
    {
        if (!$this->hasPermission('appointment.edit')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $appointmentId = $this->getPost('appointment_id');
        
        if (!$appointmentId) {
            return $this->error('預約ID不能為空', 400);
        }
        
        try {
            $appointmentModel = new AppointmentModel();
            $appointment = $appointmentModel->find($appointmentId);
            
            if (!$appointment) {
                return $this->error('預約不存在', 404);
            }
            
            if ($appointment['status'] === 'completed') {
                return $this->error('預約已完成');
            }
            
            $appointmentModel->completeAppointment($appointmentId);
            
            // 記錄操作日誌
            $this->logActivity('appointment_complete', 'appointments', $appointmentId, 
                             ['old_status' => $appointment['status']], 
                             ['new_status' => 'completed']);
            
            return $this->success(null, '預約已完成');
            
        } catch (Exception $e) {
            return $this->error('完成預約失敗：' . $e->getMessage());
        }
    }

    /**
     * 標記為未出現
     */
    public function noShowAction()
    {
        if (!$this->hasPermission('appointment.edit')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $appointmentId = $this->getPost('appointment_id');
        
        if (!$appointmentId) {
            return $this->error('預約ID不能為空', 400);
        }
        
        try {
            $appointmentModel = new AppointmentModel();
            $appointment = $appointmentModel->find($appointmentId);
            
            if (!$appointment) {
                return $this->error('預約不存在', 404);
            }
            
            $appointmentModel->markNoShow($appointmentId);
            
            // 記錄操作日誌
            $this->logActivity('appointment_no_show', 'appointments', $appointmentId, 
                             ['old_status' => $appointment['status']], 
                             ['new_status' => 'no_show']);
            
            return $this->success(null, '已標記為未出現');
            
        } catch (Exception $e) {
            return $this->error('操作失敗：' . $e->getMessage());
        }
    }

    /**
     * 獲取可用時間段（AJAX）
     */
    public function getAvailableTimeSlotsAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        $date = $this->getParam('date');
        $technicianId = $this->getParam('technician_id');
        $duration = $this->getParam('duration', 60);
        
        if (!$date) {
            return $this->error('日期不能為空', 400);
        }
        
        $appointmentModel = new AppointmentModel();
        $timeSlots = $appointmentModel->getAvailableTimeSlots($date, $technicianId, $duration);
        
        return $this->success($timeSlots);
    }

    /**
     * 今日預約
     */
    public function todayAction()
    {
        if (!$this->hasPermission('appointment.view')) {
            return $this->error('權限不足', 403);
        }
        
        $appointmentModel = new AppointmentModel();
        $appointments = $appointmentModel->getTodayAppointments();
        
        $this->getView()->assign('appointments', $appointments);
        $this->getView()->assign('title', '今日預約');
    }

    /**
     * 預約日曆
     */
    public function calendarAction()
    {
        if (!$this->hasPermission('appointment.view')) {
            return $this->error('權限不足', 403);
        }
        
        $month = $this->getParam('month', date('Y-m'));
        
        // 獲取該月的預約
        $appointmentModel = new AppointmentModel();
        $appointments = $appointmentModel->getMonthAppointments($month);
        
        $this->getView()->assign('appointments', $appointments);
        $this->getView()->assign('month', $month);
        $this->getView()->assign('title', '預約日曆');
    }
}

<?php
/**
 * 租戶模型
 */
class TenantModel extends BaseModel
{
    protected $table = 'tenants';
    protected $tenantField = null; // 租戶表本身不需要租戶過濾
    
    protected $fillable = [
        'name', 'subdomain', 'phone', 'address', 'tax_id', 
        'status', 'trial_expires_at', 'subscription_expires_at', 
        'current_plan_id', 'auto_renew'
    ];

    /**
     * 根據子域名查找租戶
     */
    public function findBySubdomain($subdomain)
    {
        $stmt = $this->db->prepare("
            SELECT t.*, sp.name as plan_name, sp.max_users, sp.max_vehicles, sp.features
            FROM tenants t 
            LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id 
            WHERE t.subdomain = ? AND t.status != 'cancelled'
        ");
        $stmt->execute([$subdomain]);
        return $stmt->fetch();
    }

    /**
     * 檢查子域名是否可用
     */
    public function isSubdomainAvailable($subdomain, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) as count FROM tenants WHERE subdomain = ?";
        $params = [$subdomain];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] == 0;
    }

    /**
     * 創建新租戶（包含預設管理員）
     */
    public function createWithAdmin($tenantData, $adminData)
    {
        $this->beginTransaction();
        
        try {
            // 創建租戶
            $tenant = $this->create($tenantData);
            if (!$tenant) {
                throw new Exception('創建租戶失敗');
            }
            
            // 創建管理員用戶
            $userModel = new UserModel();
            $adminData['tenant_id'] = $tenant['id'];
            $adminData['role'] = 'owner';
            $adminData['password'] = password_hash($adminData['password'], PASSWORD_DEFAULT);
            
            $admin = $userModel->create($adminData);
            if (!$admin) {
                throw new Exception('創建管理員失敗');
            }
            
            $this->commit();
            return $tenant;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 獲取即將到期的租戶
     */
    public function getExpiringTenants($days = 7)
    {
        $stmt = $this->db->prepare("
            SELECT t.*, sp.name as plan_name 
            FROM tenants t 
            LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id 
            WHERE t.status = 'active' 
            AND t.subscription_expires_at IS NOT NULL 
            AND t.subscription_expires_at <= DATE_ADD(NOW(), INTERVAL ? DAY)
            AND t.subscription_expires_at > NOW()
        ");
        $stmt->execute([$days]);
        return $stmt->fetchAll();
    }

    /**
     * 獲取已過期的租戶
     */
    public function getExpiredTenants()
    {
        $stmt = $this->db->prepare("
            SELECT t.*, sp.name as plan_name 
            FROM tenants t 
            LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id 
            WHERE t.status = 'active' 
            AND (
                (t.trial_expires_at IS NOT NULL AND t.trial_expires_at < NOW()) OR
                (t.subscription_expires_at IS NOT NULL AND t.subscription_expires_at < NOW())
            )
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * 更新訂閱
     */
    public function updateSubscription($tenantId, $planId, $expiresAt)
    {
        return $this->update($tenantId, [
            'current_plan_id' => $planId,
            'subscription_expires_at' => $expiresAt,
            'status' => 'active'
        ]);
    }

    /**
     * 暫停租戶
     */
    public function suspend($tenantId, $reason = null)
    {
        $data = ['status' => 'suspended'];
        if ($reason) {
            $data['suspend_reason'] = $reason;
        }
        return $this->update($tenantId, $data);
    }

    /**
     * 恢復租戶
     */
    public function resume($tenantId)
    {
        return $this->update($tenantId, [
            'status' => 'active',
            'suspend_reason' => null
        ]);
    }

    /**
     * 取消租戶
     */
    public function cancel($tenantId, $reason = null)
    {
        $data = ['status' => 'cancelled'];
        if ($reason) {
            $data['cancel_reason'] = $reason;
        }
        return $this->update($tenantId, $data);
    }

    /**
     * 獲取租戶統計
     */
    public function getStats($tenantId)
    {
        $stats = [];
        
        // 用戶數量
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE tenant_id = ? AND is_active = 1");
        $stmt->execute([$tenantId]);
        $stats['users'] = $stmt->fetch()['count'];
        
        // 客戶數量
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM customers WHERE tenant_id = ? AND is_active = 1");
        $stmt->execute([$tenantId]);
        $stats['customers'] = $stmt->fetch()['count'];
        
        // 車輛數量
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM vehicles WHERE tenant_id = ? AND is_active = 1");
        $stmt->execute([$tenantId]);
        $stats['vehicles'] = $stmt->fetch()['count'];
        
        // 本月工單數量
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM work_orders 
            WHERE tenant_id = ? 
            AND YEAR(created_at) = YEAR(NOW()) 
            AND MONTH(created_at) = MONTH(NOW())
        ");
        $stmt->execute([$tenantId]);
        $stats['monthly_orders'] = $stmt->fetch()['count'];
        
        // 本月收入
        $stmt = $this->db->prepare("
            SELECT COALESCE(SUM(final_amount), 0) as total 
            FROM work_orders 
            WHERE tenant_id = ? 
            AND status = 'completed'
            AND payment_status = 'paid'
            AND YEAR(completed_at) = YEAR(NOW()) 
            AND MONTH(completed_at) = MONTH(NOW())
        ");
        $stmt->execute([$tenantId]);
        $stats['monthly_revenue'] = $stmt->fetch()['total'];
        
        return $stats;
    }

    /**
     * 檢查租戶限制
     */
    public function checkLimits($tenantId)
    {
        $tenant = $this->find($tenantId);
        if (!$tenant) {
            return false;
        }
        
        $limits = [
            'users' => $tenant['max_users'],
            'vehicles' => $tenant['max_vehicles']
        ];
        
        $current = [
            'users' => $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE tenant_id = ? AND is_active = 1"),
            'vehicles' => $this->db->prepare("SELECT COUNT(*) as count FROM vehicles WHERE tenant_id = ? AND is_active = 1")
        ];
        
        $result = [];
        foreach ($current as $type => $stmt) {
            $stmt->execute([$tenantId]);
            $count = $stmt->fetch()['count'];
            $result[$type] = [
                'current' => $count,
                'limit' => $limits[$type],
                'exceeded' => $limits[$type] && $count >= $limits[$type]
            ];
        }
        
        return $result;
    }
}

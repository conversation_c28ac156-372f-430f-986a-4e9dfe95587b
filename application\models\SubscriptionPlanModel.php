<?php
/**
 * 訂閱方案模型
 */
class SubscriptionPlanModel extends BaseModel
{
    protected $table = 'subscription_plans';
    protected $tenantField = null; // 訂閱方案是全域的
    
    protected $fillable = [
        'name', 'price', 'duration_months', 'max_users', 'max_vehicles', 'features', 'is_active'
    ];

    /**
     * 獲取所有啟用的方案
     */
    public function getActivePlans()
    {
        return $this->findAll(['is_active' => 1], 'price ASC');
    }

    /**
     * 獲取方案詳情
     */
    public function getPlanDetails($planId)
    {
        $plan = $this->find($planId);
        if ($plan && $plan['features']) {
            $plan['features'] = json_decode($plan['features'], true);
        }
        return $plan;
    }

    /**
     * 檢查方案是否可用
     */
    public function isPlanAvailable($planId)
    {
        $plan = $this->find($planId);
        return $plan && $plan['is_active'];
    }

    /**
     * 獲取方案統計
     */
    public function getPlanStats()
    {
        $stats = [];
        
        // 每個方案的租戶數量
        $stmt = $this->db->prepare("
            SELECT sp.id, sp.name, COUNT(t.id) as tenant_count
            FROM subscription_plans sp
            LEFT JOIN tenants t ON sp.id = t.current_plan_id AND t.status = 'active'
            WHERE sp.is_active = 1
            GROUP BY sp.id, sp.name
            ORDER BY sp.price ASC
        ");
        $stmt->execute();
        $stats['by_plan'] = $stmt->fetchAll();
        
        // 總收入統計
        $stmt = $this->db->prepare("
            SELECT sp.name, SUM(po.amount) as total_revenue
            FROM subscription_plans sp
            LEFT JOIN payment_orders po ON sp.id = po.plan_id AND po.status = 'paid'
            WHERE sp.is_active = 1
            GROUP BY sp.id, sp.name
            ORDER BY total_revenue DESC
        ");
        $stmt->execute();
        $stats['revenue_by_plan'] = $stmt->fetchAll();
        
        return $stats;
    }

    /**
     * 創建方案
     */
    public function create($data)
    {
        // 處理 features JSON
        if (isset($data['features']) && is_array($data['features'])) {
            $data['features'] = json_encode($data['features'], JSON_UNESCAPED_UNICODE);
        }
        
        return parent::create($data);
    }

    /**
     * 更新方案
     */
    public function update($id, $data)
    {
        // 處理 features JSON
        if (isset($data['features']) && is_array($data['features'])) {
            $data['features'] = json_encode($data['features'], JSON_UNESCAPED_UNICODE);
        }
        
        return parent::update($id, $data);
    }
}

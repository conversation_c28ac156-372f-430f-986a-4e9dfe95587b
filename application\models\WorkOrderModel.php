<?php
/**
 * 工單模型
 */
class WorkOrderModel extends BaseModel
{
    protected $table = 'work_orders';
    
    protected $fillable = [
        'order_no', 'customer_id', 'vehicle_id', 'technician_id', 'status', 'type',
        'description', 'customer_complaint', 'diagnosis', 'work_performed',
        'mileage_in', 'mileage_out', 'estimated_hours', 'actual_hours',
        'labor_cost', 'parts_cost', 'total_cost', 'discount', 'final_amount',
        'payment_status', 'started_at', 'completed_at'
    ];

    /**
     * 搜索工單
     */
    public function search($query, $limit = 10)
    {
        $sql = "
            SELECT wo.*, c.name as customer_name, v.license_plate, u.full_name as technician_name
            FROM work_orders wo
            LEFT JOIN customers c ON wo.customer_id = c.id
            LEFT JOIN vehicles v ON wo.vehicle_id = v.id
            LEFT JOIN users u ON wo.technician_id = u.id
            WHERE wo.tenant_id = ?
            AND (
                wo.order_no LIKE ? OR
                c.name LIKE ? OR
                v.license_plate LIKE ? OR
                wo.description LIKE ?
            )
            ORDER BY wo.created_at DESC
            LIMIT ?
        ";
        
        $searchTerm = "%{$query}%";
        $tenantId = $this->getCurrentTenantId();
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * 生成工單編號
     */
    public function generateOrderNo()
    {
        $tenantId = $this->getCurrentTenantId();
        $prefix = 'WO' . date('Ymd');
        
        // 獲取當日最大編號
        $stmt = $this->db->prepare("
            SELECT order_no 
            FROM work_orders 
            WHERE tenant_id = ? 
            AND order_no LIKE ? 
            ORDER BY order_no DESC 
            LIMIT 1
        ");
        $stmt->execute([$tenantId, $prefix . '%']);
        $lastOrder = $stmt->fetch();
        
        if ($lastOrder) {
            $lastNo = intval(substr($lastOrder['order_no'], -3));
            $newNo = $lastNo + 1;
        } else {
            $newNo = 1;
        }
        
        return $prefix . str_pad($newNo, 3, '0', STR_PAD_LEFT);
    }

    /**
     * 創建工單
     */
    public function create($data)
    {
        // 自動生成工單編號
        if (empty($data['order_no'])) {
            $data['order_no'] = $this->generateOrderNo();
        }
        
        return parent::create($data);
    }

    /**
     * 獲取今日工單
     */
    public function getTodayOrders($limit = null)
    {
        $sql = "
            SELECT wo.*, c.name as customer_name, v.license_plate, u.full_name as technician_name
            FROM work_orders wo
            LEFT JOIN customers c ON wo.customer_id = c.id
            LEFT JOIN vehicles v ON wo.vehicle_id = v.id
            LEFT JOIN users u ON wo.technician_id = u.id
            WHERE wo.tenant_id = ?
            AND DATE(wo.created_at) = CURDATE()
            ORDER BY wo.created_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->getCurrentTenantId()]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取待處理工單
     */
    public function getPendingOrders($limit = null)
    {
        $sql = "
            SELECT wo.*, c.name as customer_name, v.license_plate, u.full_name as technician_name
            FROM work_orders wo
            LEFT JOIN customers c ON wo.customer_id = c.id
            LEFT JOIN vehicles v ON wo.vehicle_id = v.id
            LEFT JOIN users u ON wo.technician_id = u.id
            WHERE wo.tenant_id = ?
            AND wo.status IN ('pending', 'in_progress')
            ORDER BY wo.created_at ASC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->getCurrentTenantId()]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取客戶最近工單
     */
    public function getCustomerRecentOrders($customerId, $limit = 5)
    {
        $sql = "
            SELECT wo.*, v.license_plate, u.full_name as technician_name
            FROM work_orders wo
            LEFT JOIN vehicles v ON wo.vehicle_id = v.id
            LEFT JOIN users u ON wo.technician_id = u.id
            WHERE wo.customer_id = ?
            ORDER BY wo.created_at DESC
            LIMIT ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$customerId, $limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取車輛維修歷史
     */
    public function getVehicleHistory($vehicleId, $limit = null)
    {
        $sql = "
            SELECT wo.*, c.name as customer_name, u.full_name as technician_name
            FROM work_orders wo
            LEFT JOIN customers c ON wo.customer_id = c.id
            LEFT JOIN users u ON wo.technician_id = u.id
            WHERE wo.vehicle_id = ?
            ORDER BY wo.created_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$vehicleId]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取工單詳情
     */
    public function getOrderDetails($orderId)
    {
        $sql = "
            SELECT wo.*, 
                   c.name as customer_name, c.phone as customer_phone, c.email as customer_email,
                   v.license_plate, v.brand, v.model, v.year,
                   u.full_name as technician_name
            FROM work_orders wo
            LEFT JOIN customers c ON wo.customer_id = c.id
            LEFT JOIN vehicles v ON wo.vehicle_id = v.id
            LEFT JOIN users u ON wo.technician_id = u.id
            WHERE wo.id = ? AND wo.tenant_id = ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$orderId, $this->getCurrentTenantId()]);
        $order = $stmt->fetch();
        
        if (!$order) {
            return null;
        }
        
        // 獲取使用的零件
        $partModel = new WorkOrderPartModel();
        $order['parts'] = $partModel->getOrderParts($orderId);
        
        return $order;
    }

    /**
     * 更新工單狀態
     */
    public function updateStatus($orderId, $status, $additionalData = [])
    {
        $data = array_merge(['status' => $status], $additionalData);
        
        // 根據狀態設定時間戳
        switch ($status) {
            case 'in_progress':
                if (empty($data['started_at'])) {
                    $data['started_at'] = date('Y-m-d H:i:s');
                }
                break;
            case 'completed':
                if (empty($data['completed_at'])) {
                    $data['completed_at'] = date('Y-m-d H:i:s');
                }
                break;
        }
        
        return $this->update($orderId, $data);
    }

    /**
     * 計算工單總費用
     */
    public function calculateTotalCost($orderId)
    {
        $order = $this->find($orderId);
        if (!$order) {
            return false;
        }
        
        // 計算零件費用
        $partModel = new WorkOrderPartModel();
        $partsCost = $partModel->getOrderPartsCost($orderId);
        
        $laborCost = $order['labor_cost'] ?? 0;
        $totalCost = $laborCost + $partsCost;
        $discount = $order['discount'] ?? 0;
        $finalAmount = $totalCost - $discount;
        
        return $this->update($orderId, [
            'parts_cost' => $partsCost,
            'total_cost' => $totalCost,
            'final_amount' => $finalAmount
        ]);
    }

    /**
     * 獲取工單統計
     */
    public function getStats($period = 'month')
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stats = [];
        
        // 根據期間設定日期條件
        switch ($period) {
            case 'today':
                $dateCondition = "DATE(created_at) = CURDATE()";
                break;
            case 'week':
                $dateCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $dateCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            case 'year':
                $dateCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
                break;
            default:
                $dateCondition = "1=1";
        }
        
        // 按狀態統計
        $stmt = $this->db->prepare("
            SELECT status, COUNT(*) as count 
            FROM work_orders 
            WHERE tenant_id = ? AND {$dateCondition}
            GROUP BY status
        ");
        $stmt->execute([$tenantId]);
        $statusStats = $stmt->fetchAll();
        $stats['by_status'] = [];
        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = $stat['count'];
        }
        
        // 按類型統計
        $stmt = $this->db->prepare("
            SELECT type, COUNT(*) as count 
            FROM work_orders 
            WHERE tenant_id = ? AND {$dateCondition}
            GROUP BY type
        ");
        $stmt->execute([$tenantId]);
        $typeStats = $stmt->fetchAll();
        $stats['by_type'] = [];
        foreach ($typeStats as $stat) {
            $stats['by_type'][$stat['type']] = $stat['count'];
        }
        
        // 收入統計
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_orders,
                SUM(final_amount) as total_revenue,
                AVG(final_amount) as avg_order_value
            FROM work_orders 
            WHERE tenant_id = ? 
            AND status = 'completed' 
            AND {$dateCondition}
        ");
        $stmt->execute([$tenantId]);
        $revenueStats = $stmt->fetch();
        $stats['revenue'] = $revenueStats;
        
        return $stats;
    }
}

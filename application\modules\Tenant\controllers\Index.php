<?php
/**
 * 租戶控制器
 */
class IndexController extends BaseController
{
    /**
     * 租戶首頁
     */
    public function indexAction()
    {
        // 如果沒有租戶資訊，重定向到註冊頁面
        if (!$this->tenant) {
            return $this->redirect('/tenant/register');
        }

        // 如果用戶已登入，重定向到儀表板
        if ($this->user) {
            return $this->redirect('/dashboard');
        }

        // 顯示租戶登入頁面
        $this->getView()->assign('tenant', $this->tenant);
        $this->getView()->assign('title', $this->tenant['name'] . ' - 登入');
    }

    /**
     * 租戶註冊
     */
    public function registerAction()
    {
        if ($this->isPost()) {
            return $this->handleRegister();
        }

        // 獲取訂閱方案
        $planModel = new SubscriptionPlanModel();
        $plans = $planModel->getActivePlans();

        $this->getView()->assign('plans', $plans);
        $this->getView()->assign('title', '租戶註冊');
    }

    /**
     * 處理租戶註冊
     */
    private function handleRegister()
    {
        $data = $this->getPost();

        // 驗證輸入
        $validation = $this->validate($data, [
            'tenant_name' => 'required|max:100',
            'subdomain' => 'required|min:3|max:50',
            'phone' => 'phone',
            'address' => 'max:255',
            'tax_id' => 'max:20',
            'admin_name' => 'required|max:100',
            'admin_email' => 'required|email|max:100',
            'admin_username' => 'required|min:3|max:50',
            'admin_password' => 'required|min:6',
            'admin_password_confirmation' => 'required|confirmed',
            'plan_id' => 'required|integer',
            'agree_terms' => 'required'
        ]);

        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }

        // 檢查子域名格式
        if (!preg_match('/^[a-z0-9][a-z0-9-]*[a-z0-9]$/', $data['subdomain'])) {
            if ($this->isAjax()) {
                return $this->error('子域名格式不正確，只能包含小寫字母、數字和連字符');
            }
            $this->getView()->assign('error', '子域名格式不正確');
            $this->getView()->assign('old_data', $data);
            return;
        }

        // 檢查子域名是否可用
        $tenantModel = new TenantModel();
        if (!$tenantModel->isSubdomainAvailable($data['subdomain'])) {
            if ($this->isAjax()) {
                return $this->error('子域名已被使用');
            }
            $this->getView()->assign('error', '子域名已被使用');
            $this->getView()->assign('old_data', $data);
            return;
        }

        // 檢查方案是否有效
        $planModel = new SubscriptionPlanModel();
        if (!$planModel->isPlanAvailable($data['plan_id'])) {
            if ($this->isAjax()) {
                return $this->error('選擇的方案無效');
            }
            $this->getView()->assign('error', '選擇的方案無效');
            $this->getView()->assign('old_data', $data);
            return;
        }

        try {
            // 創建租戶和管理員
            $tenantData = [
                'name' => $data['tenant_name'],
                'subdomain' => $data['subdomain'],
                'phone' => $data['phone'] ?? null,
                'address' => $data['address'] ?? null,
                'tax_id' => $data['tax_id'] ?? null,
                'current_plan_id' => $data['plan_id'],
                'status' => 'trial',
                'trial_expires_at' => date('Y-m-d H:i:s', strtotime('+14 days'))
            ];

            $adminData = [
                'full_name' => $data['admin_name'],
                'email' => $data['admin_email'],
                'username' => $data['admin_username'],
                'password' => $data['admin_password']
            ];

            $tenant = $tenantModel->createWithAdmin($tenantData, $adminData);

            // 記錄註冊日誌
            $this->logActivity('tenant_register', 'tenants', $tenant['id'], null, $tenantData);

            if ($this->isAjax()) {
                return $this->success([
                    'tenant_id' => $tenant['id'],
                    'subdomain' => $tenant['subdomain'],
                    'redirect' => '/tenant/' . $tenant['subdomain'] . '?registered=1'
                ], '註冊成功！請使用您的帳號登入。');
            }

            return $this->redirect('/tenant/' . $tenant['subdomain'] . '?registered=1');

        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('註冊失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '註冊失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 訂閱管理
     */
    public function subscriptionAction()
    {
        if (!$this->tenant || !$this->user) {
            return $this->redirect('/');
        }

        // 檢查權限
        if (!$this->hasPermission('setting.view')) {
            return $this->error('權限不足', 403);
        }

        $tenantModel = new TenantModel();
        $planModel = new SubscriptionPlanModel();

        // 獲取當前租戶詳情
        $tenant = $tenantModel->find($this->tenant['id']);
        $currentPlan = $planModel->find($tenant['current_plan_id']);

        // 獲取所有可用方案
        $availablePlans = $planModel->getActivePlans();

        // 獲取訂閱歷史
        $subscriptionModel = new SubscriptionModel();
        $subscriptionHistory = $subscriptionModel->getTenantSubscriptions($this->tenant['id']);

        $this->getView()->assign('tenant', $tenant);
        $this->getView()->assign('currentPlan', $currentPlan);
        $this->getView()->assign('availablePlans', $availablePlans);
        $this->getView()->assign('subscriptionHistory', $subscriptionHistory);
        $this->getView()->assign('title', '訂閱管理');
    }

    /**
     * 升級訂閱
     */
    public function upgradeAction()
    {
        if (!$this->tenant || !$this->user) {
            return $this->error('請先登入', 401);
        }

        if (!$this->hasPermission('setting.edit')) {
            return $this->error('權限不足', 403);
        }

        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }

        $planId = $this->getPost('plan_id');
        $duration = $this->getPost('duration', 1); // 預設1個月

        // 驗證輸入
        $validation = $this->validate([
            'plan_id' => $planId,
            'duration' => $duration
        ], [
            'plan_id' => 'required|integer',
            'duration' => 'required|integer|min:1|max:12'
        ]);

        if (!$validation['valid']) {
            return $this->error('請檢查輸入資料', 400, $validation['errors']);
        }

        // 檢查方案是否有效
        $planModel = new SubscriptionPlanModel();
        $plan = $planModel->find($planId);
        if (!$plan || !$plan['is_active']) {
            return $this->error('選擇的方案無效');
        }

        try {
            // 創建支付訂單
            $paymentModel = new PaymentOrderModel();
            $amount = $plan['price'] * $duration;

            $orderData = [
                'tenant_id' => $this->tenant['id'],
                'plan_id' => $planId,
                'amount' => $amount,
                'duration_months' => $duration
            ];

            $order = $paymentModel->createOrder($orderData);

            return $this->success([
                'order_id' => $order['id'],
                'order_no' => $order['order_no'],
                'amount' => $amount,
                'redirect' => '/payment/checkout/' . $order['order_no']
            ], '訂單創建成功，正在跳轉到支付頁面...');

        } catch (Exception $e) {
            return $this->error('創建訂單失敗：' . $e->getMessage());
        }
    }

    /**
     * 取消訂閱
     */
    public function cancelAction()
    {
        if (!$this->tenant || !$this->user) {
            return $this->error('請先登入', 401);
        }

        if (!$this->hasPermission('setting.edit')) {
            return $this->error('權限不足', 403);
        }

        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }

        $reason = $this->getPost('reason', '');

        try {
            $tenantModel = new TenantModel();
            $tenantModel->cancel($this->tenant['id'], $reason);

            // 記錄取消日誌
            $this->logActivity('subscription_cancel', 'tenants', $this->tenant['id'], null, ['reason' => $reason]);

            // 清除 session
            session_destroy();

            return $this->success(['redirect' => '/'], '訂閱已取消');

        } catch (Exception $e) {
            return $this->error('取消訂閱失敗：' . $e->getMessage());
        }
    }
}
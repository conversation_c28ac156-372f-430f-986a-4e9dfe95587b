# Nginx 配置文件範例
# 適用於汽車保養廠管理系統

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # HTTP 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # 文件根目錄
    root /web/car-maintenance/public;
    index index.php index.html;
    
    # SSL 憑證設定
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # SSL 安全設定
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全標頭
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 檔案上傳大小限制
    client_max_body_size 50M;
    
    # Gzip 壓縮
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # 靜態檔案快取
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 隱藏敏感檔案
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(ini|conf|sql)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止訪問特定目錄
    location ~ ^/(application|database|docs|tests|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # PHP 檔案處理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # PHP 安全設定
        fastcgi_param PHP_VALUE "upload_max_filesize=50M \n post_max_size=50M";
        fastcgi_read_timeout 300;
    }
    
    # Yaf 框架路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # API 路由
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # 支付回調（特殊處理）
    location /payment/ecpay-notify {
        try_files $uri /index.php?$query_string;
        access_log /var/log/nginx/payment.log;
    }
    
    # 上傳檔案目錄
    location /uploads/ {
        # 禁止執行 PHP
        location ~ \.php$ {
            deny all;
        }
        
        # 檔案類型限制
        location ~* \.(php|phtml|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }
    
    # 健康檢查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 錯誤頁面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 日誌設定
    access_log /var/log/nginx/car-maintenance.access.log;
    error_log /var/log/nginx/car-maintenance.error.log;
}

# 多租戶子域名配置
server {
    listen 443 ssl http2;
    server_name *.your-domain.com;
    
    # SSL 憑證（萬用字元憑證）
    ssl_certificate /etc/ssl/certs/wildcard.your-domain.crt;
    ssl_certificate_key /etc/ssl/private/wildcard.your-domain.key;
    
    # 其他設定同上
    root /web/car-maintenance/public;
    index index.php index.html;
    
    # 傳遞子域名資訊給 PHP
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param HTTP_HOST $host;
        include fastcgi_params;
    }
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}

# 負載平衡配置（如有多台伺服器）
upstream backend {
    server 127.0.0.1:9000;
    # server 127.0.0.1:9001;
    # server 127.0.0.1:9002;
}

# 限制請求頻率
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

server {
    # ... 其他配置 ...
    
    # 登入頁面限制
    location /auth/login {
        limit_req zone=login burst=3 nodelay;
        try_files $uri /index.php?$query_string;
    }
    
    # API 限制
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri /index.php?$query_string;
    }
}

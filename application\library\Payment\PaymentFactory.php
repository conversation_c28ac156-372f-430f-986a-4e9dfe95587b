<?php
/**
 * 支付工廠類別
 * 支援多種支付方式
 */
class PaymentFactory
{
    /**
     * 創建支付服務實例
     */
    public static function create($provider = 'ecpay')
    {
        switch (strtolower($provider)) {
            case 'ecpay':
                return new EcpayService();
            
            case 'newebpay':
                return new NewebPayService();
            
            case 'linepay':
                return new LinePayService();
            
            default:
                throw new Exception('不支援的支付方式: ' . $provider);
        }
    }

    /**
     * 獲取支援的支付方式列表
     */
    public static function getSupportedProviders()
    {
        return [
            'ecpay' => [
                'name' => '綠界科技',
                'description' => '支援信用卡、ATM、超商代碼等多種支付方式',
                'enabled' => true
            ],
            'newebpay' => [
                'name' => '藍新金流',
                'description' => '支援信用卡、WebATM、超商代碼等支付方式',
                'enabled' => false
            ],
            'linepay' => [
                'name' => 'LINE Pay',
                'description' => '使用 LINE Pay 進行支付',
                'enabled' => false
            ]
        ];
    }

    /**
     * 驗證支付方式是否可用
     */
    public static function isProviderEnabled($provider)
    {
        $providers = self::getSupportedProviders();
        return isset($providers[$provider]) && $providers[$provider]['enabled'];
    }
}

/**
 * 藍新金流服務（預留）
 */
class NewebPayService
{
    public function generatePaymentForm($order)
    {
        throw new Exception('藍新金流服務尚未實現');
    }

    public function handleNotify($postData)
    {
        throw new Exception('藍新金流服務尚未實現');
    }
}

/**
 * LINE Pay 服務（預留）
 */
class LinePayService
{
    public function generatePaymentForm($order)
    {
        throw new Exception('LINE Pay 服務尚未實現');
    }

    public function handleNotify($postData)
    {
        throw new Exception('LINE Pay 服務尚未實現');
    }
}

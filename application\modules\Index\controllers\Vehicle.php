<?php
/**
 * 車輛管理控制器
 */
class VehicleController extends BaseController
{
    /**
     * 車輛列表
     */
    public function indexAction()
    {
        if (!$this->hasPermission('vehicle.view')) {
            return $this->error('權限不足', 403);
        }
        
        $page = $this->getParam('page', 1);
        $search = $this->getParam('search', '');
        $customerId = $this->getParam('customer_id', '');
        $perPage = 15;
        
        $vehicleModel = new VehicleModel();
        $query = $vehicleModel->query()
            ->select('v.*, c.name as customer_name, c.phone as customer_phone')
            ->leftJoin('customers c', 'v.customer_id = c.id');
        
        // 搜索條件
        if (!empty($search)) {
            $query->where("(v.license_plate LIKE ? OR v.brand LIKE ? OR v.model LIKE ? OR c.name LIKE ?)", 
                         "%{$search}%", "%{$search}%", "%{$search}%", "%{$search}%");
        }
        
        // 客戶篩選
        if (!empty($customerId)) {
            $query->where('v.customer_id', $customerId);
        }
        
        $query->orderBy('v.created_at', 'DESC');
        
        $result = $this->paginate($query, $page, $perPage);
        
        // 獲取統計數據
        $stats = $vehicleModel->getStats();
        
        // 獲取客戶列表（用於篩選）
        $customerModel = new CustomerModel();
        $customers = $customerModel->findAll(['is_active' => 1], 'name ASC');
        
        $this->getView()->assign('vehicles', $result['data']);
        $this->getView()->assign('pagination', $result['pagination']);
        $this->getView()->assign('search', $search);
        $this->getView()->assign('customerId', $customerId);
        $this->getView()->assign('customers', $customers);
        $this->getView()->assign('stats', $stats);
        $this->getView()->assign('title', '車輛管理');
    }

    /**
     * 車輛詳情
     */
    public function viewAction()
    {
        if (!$this->hasPermission('vehicle.view')) {
            return $this->error('權限不足', 403);
        }
        
        $vehicleId = $this->getParam('id');
        if (!$vehicleId) {
            return $this->error('車輛ID不能為空', 400);
        }
        
        $vehicleModel = new VehicleModel();
        $vehicle = $vehicleModel->getVehicleDetails($vehicleId);
        
        if (!$vehicle) {
            return $this->error('車輛不存在', 404);
        }
        
        // 獲取維修頻率統計
        $maintenanceFrequency = $vehicleModel->getMaintenanceFrequency($vehicleId, 12);
        
        $this->getView()->assign('vehicle', $vehicle);
        $this->getView()->assign('maintenanceFrequency', $maintenanceFrequency);
        $this->getView()->assign('title', '車輛詳情 - ' . $vehicle['license_plate']);
    }

    /**
     * 新增車輛
     */
    public function addAction()
    {
        if (!$this->hasPermission('vehicle.create')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleAddVehicle();
        }
        
        // 獲取客戶列表
        $customerModel = new CustomerModel();
        $customers = $customerModel->findAll(['is_active' => 1], 'name ASC');
        
        // 預設客戶ID（如果從客戶頁面跳轉過來）
        $defaultCustomerId = $this->getParam('customer_id');
        
        $this->getView()->assign('customers', $customers);
        $this->getView()->assign('defaultCustomerId', $defaultCustomerId);
        $this->getView()->assign('title', '新增車輛');
    }

    /**
     * 處理新增車輛
     */
    private function handleAddVehicle()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'customer_id' => 'required|integer',
            'license_plate' => 'required|license_plate|max:20',
            'brand' => 'required|max:50',
            'model' => 'required|max:50',
            'year' => 'integer|min:1900|max:' . (date('Y') + 1),
            'engine_no' => 'max:50',
            'chassis_no' => 'max:50',
            'color' => 'max:20',
            'fuel_type' => 'in:gasoline,diesel,hybrid,electric',
            'mileage' => 'integer|min:0',
            'notes' => 'max:500'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        try {
            $vehicleModel = new VehicleModel();
            
            // 檢查車牌是否已存在
            if (!$vehicleModel->isLicensePlateAvailable($data['license_plate'])) {
                throw new Exception('車牌號碼已存在');
            }
            
            // 檢查客戶是否存在
            $customerModel = new CustomerModel();
            $customer = $customerModel->find($data['customer_id']);
            if (!$customer) {
                throw new Exception('客戶不存在');
            }
            
            $vehicleData = [
                'customer_id' => $data['customer_id'],
                'license_plate' => strtoupper($data['license_plate']),
                'brand' => $data['brand'],
                'model' => $data['model'],
                'year' => $data['year'] ?? null,
                'engine_no' => $data['engine_no'] ?? null,
                'chassis_no' => $data['chassis_no'] ?? null,
                'color' => $data['color'] ?? null,
                'fuel_type' => $data['fuel_type'] ?? null,
                'mileage' => $data['mileage'] ?? 0,
                'notes' => $data['notes'] ?? null
            ];
            
            $vehicle = $vehicleModel->create($vehicleData);
            
            // 記錄操作日誌
            $this->logActivity('vehicle_create', 'vehicles', $vehicle['id'], null, $vehicleData);
            
            if ($this->isAjax()) {
                return $this->success(['vehicle_id' => $vehicle['id']], '車輛新增成功');
            }
            
            return $this->redirect('/vehicle/view/' . $vehicle['id'] . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('新增失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '新增失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 編輯車輛
     */
    public function editAction()
    {
        if (!$this->hasPermission('vehicle.edit')) {
            return $this->error('權限不足', 403);
        }
        
        $vehicleId = $this->getParam('id');
        if (!$vehicleId) {
            return $this->error('車輛ID不能為空', 400);
        }
        
        $vehicleModel = new VehicleModel();
        $vehicle = $vehicleModel->find($vehicleId);
        
        if (!$vehicle) {
            return $this->error('車輛不存在', 404);
        }
        
        if ($this->isPost()) {
            return $this->handleEditVehicle($vehicleId);
        }
        
        // 獲取客戶列表
        $customerModel = new CustomerModel();
        $customers = $customerModel->findAll(['is_active' => 1], 'name ASC');
        
        $this->getView()->assign('vehicle', $vehicle);
        $this->getView()->assign('customers', $customers);
        $this->getView()->assign('title', '編輯車輛 - ' . $vehicle['license_plate']);
    }

    /**
     * 處理編輯車輛
     */
    private function handleEditVehicle($vehicleId)
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'customer_id' => 'required|integer',
            'license_plate' => 'required|license_plate|max:20',
            'brand' => 'required|max:50',
            'model' => 'required|max:50',
            'year' => 'integer|min:1900|max:' . (date('Y') + 1),
            'engine_no' => 'max:50',
            'chassis_no' => 'max:50',
            'color' => 'max:20',
            'fuel_type' => 'in:gasoline,diesel,hybrid,electric',
            'mileage' => 'integer|min:0',
            'notes' => 'max:500'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        try {
            $vehicleModel = new VehicleModel();
            $oldData = $vehicleModel->find($vehicleId);
            
            // 檢查車牌是否已存在（排除當前車輛）
            if (!$vehicleModel->isLicensePlateAvailable($data['license_plate'], $vehicleId)) {
                throw new Exception('車牌號碼已存在');
            }
            
            // 檢查客戶是否存在
            $customerModel = new CustomerModel();
            $customer = $customerModel->find($data['customer_id']);
            if (!$customer) {
                throw new Exception('客戶不存在');
            }
            
            // 檢查里程是否合理（不能小於當前里程）
            if (isset($data['mileage']) && $data['mileage'] < $oldData['mileage']) {
                throw new Exception('新里程不能小於當前里程');
            }
            
            $updateData = [
                'customer_id' => $data['customer_id'],
                'license_plate' => strtoupper($data['license_plate']),
                'brand' => $data['brand'],
                'model' => $data['model'],
                'year' => $data['year'] ?? null,
                'engine_no' => $data['engine_no'] ?? null,
                'chassis_no' => $data['chassis_no'] ?? null,
                'color' => $data['color'] ?? null,
                'fuel_type' => $data['fuel_type'] ?? null,
                'mileage' => $data['mileage'] ?? $oldData['mileage'],
                'notes' => $data['notes'] ?? null
            ];
            
            $vehicleModel->update($vehicleId, $updateData);
            
            // 記錄操作日誌
            $this->logActivity('vehicle_update', 'vehicles', $vehicleId, $oldData, $updateData);
            
            if ($this->isAjax()) {
                return $this->success(null, '車輛更新成功');
            }
            
            return $this->redirect('/vehicle/view/' . $vehicleId . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 刪除車輛
     */
    public function deleteAction()
    {
        if (!$this->hasPermission('vehicle.delete')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $vehicleId = $this->getPost('vehicle_id');
        if (!$vehicleId) {
            return $this->error('車輛ID不能為空', 400);
        }
        
        try {
            $vehicleModel = new VehicleModel();
            $vehicle = $vehicleModel->find($vehicleId);
            
            if (!$vehicle) {
                return $this->error('車輛不存在', 404);
            }
            
            // 檢查是否可以刪除
            if (!$vehicleModel->canDelete($vehicleId)) {
                return $this->error('該車輛有關聯的工單或預約，無法刪除');
            }
            
            // 軟刪除
            $vehicleModel->softDelete($vehicleId);
            
            // 記錄操作日誌
            $this->logActivity('vehicle_delete', 'vehicles', $vehicleId, $vehicle);
            
            return $this->success(null, '車輛刪除成功');
            
        } catch (Exception $e) {
            return $this->error('刪除失敗：' . $e->getMessage());
        }
    }

    /**
     * 搜索車輛（AJAX）
     */
    public function searchAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        if (!$this->hasPermission('vehicle.view')) {
            return $this->error('權限不足', 403);
        }
        
        $query = $this->getParam('q', '');
        $limit = $this->getParam('limit', 10);
        
        if (strlen($query) < 2) {
            return $this->success([]);
        }
        
        $vehicleModel = new VehicleModel();
        $vehicles = $vehicleModel->search($query, $limit);
        
        return $this->success($vehicles);
    }

    /**
     * 更新里程
     */
    public function updateMileageAction()
    {
        if (!$this->hasPermission('vehicle.edit')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $vehicleId = $this->getPost('vehicle_id');
        $newMileage = $this->getPost('mileage');
        
        if (!$vehicleId || !is_numeric($newMileage)) {
            return $this->error('參數錯誤', 400);
        }
        
        try {
            $vehicleModel = new VehicleModel();
            $vehicleModel->updateMileage($vehicleId, $newMileage);
            
            // 記錄操作日誌
            $this->logActivity('vehicle_mileage_update', 'vehicles', $vehicleId, null, ['mileage' => $newMileage]);
            
            return $this->success(null, '里程更新成功');
            
        } catch (Exception $e) {
            return $this->error('更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 保養提醒
     */
    public function maintenanceReminderAction()
    {
        if (!$this->hasPermission('vehicle.view')) {
            return $this->error('權限不足', 403);
        }
        
        $vehicleModel = new VehicleModel();
        $vehicles = $vehicleModel->getVehiclesNeedingMaintenance();
        
        $this->getView()->assign('vehicles', $vehicles);
        $this->getView()->assign('title', '保養提醒');
    }
}

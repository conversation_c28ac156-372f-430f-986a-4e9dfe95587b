@echo off
echo ========================================
echo 汽車保養廠管理系統 - XAMPP 環境設定
echo ========================================
echo.

REM 檢查是否以管理員身份執行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] 以管理員身份執行
) else (
    echo [錯誤] 請以管理員身份執行此腳本
    pause
    exit /b 1
)

REM 設定變數
set XAMPP_PATH=C:\xampp
set PROJECT_PATH=%XAMPP_PATH%\htdocs\car-maintenance
set HOSTS_FILE=C:\Windows\System32\drivers\etc\hosts

echo.
echo 1. 檢查 XAMPP 安裝...
if not exist "%XAMPP_PATH%" (
    echo [錯誤] 找不到 XAMPP 安裝目錄: %XAMPP_PATH%
    echo 請確認 XAMPP 已正確安裝
    pause
    exit /b 1
)
echo [OK] XAMPP 目錄存在

echo.
echo 2. 檢查專案目錄...
if not exist "%PROJECT_PATH%" (
    echo [錯誤] 找不到專案目錄: %PROJECT_PATH%
    echo 請確認專案檔案已複製到正確位置
    pause
    exit /b 1
)
echo [OK] 專案目錄存在

echo.
echo 3. 設定 hosts 檔案...
findstr /C:"car-maintenance.local" "%HOSTS_FILE%" >nul
if %errorLevel% == 0 (
    echo [OK] hosts 檔案已設定
) else (
    echo 127.0.0.1 car-maintenance.local >> "%HOSTS_FILE%"
    echo 127.0.0.1 www.car-maintenance.local >> "%HOSTS_FILE%"
    echo [OK] hosts 檔案設定完成
)

echo.
echo 4. 複製配置檔案...
if not exist "%PROJECT_PATH%\application\conf\application.ini" (
    copy "%PROJECT_PATH%\application\conf\application.ini.xampp" "%PROJECT_PATH%\application\conf\application.ini"
    echo [OK] 配置檔案複製完成
) else (
    echo [OK] 配置檔案已存在
)

echo.
echo 5. 建立必要目錄...
if not exist "%PROJECT_PATH%\application\storage" mkdir "%PROJECT_PATH%\application\storage"
if not exist "%PROJECT_PATH%\application\storage\logs" mkdir "%PROJECT_PATH%\application\storage\logs"
if not exist "%PROJECT_PATH%\application\storage\cache" mkdir "%PROJECT_PATH%\application\storage\cache"
if not exist "%PROJECT_PATH%\public\uploads" mkdir "%PROJECT_PATH%\public\uploads"
echo [OK] 目錄建立完成

echo.
echo 6. 檢查 PHP 擴展...
"%XAMPP_PATH%\php\php.exe" -m | findstr /C:"yaf" >nul
if %errorLevel% == 0 (
    echo [OK] Yaf 擴展已安裝
) else (
    echo [警告] Yaf 擴展未安裝
    echo 請手動安裝 Yaf 擴展：
    echo 1. 下載 php_yaf.dll
    echo 2. 複製到 %XAMPP_PATH%\php\ext\
    echo 3. 在 php.ini 中添加 extension=yaf
    echo 4. 重啟 Apache
)

echo.
echo 7. 設定 Apache 虛擬主機...
findstr /C:"car-maintenance.local" "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf" >nul
if %errorLevel% == 0 (
    echo [OK] 虛擬主機已設定
) else (
    echo. >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo ^<VirtualHost *:80^> >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo     DocumentRoot "C:/xampp/htdocs/car-maintenance/public" >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo     ServerName car-maintenance.local >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo     ServerAlias www.car-maintenance.local >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo     ^<Directory "C:/xampp/htdocs/car-maintenance/public"^> >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo         AllowOverride All >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo         Require all granted >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo         DirectoryIndex index.php >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo     ^</Directory^> >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo ^</VirtualHost^> >> "%XAMPP_PATH%\apache\conf\extra\httpd-vhosts.conf"
    echo [OK] 虛擬主機設定完成
)

echo.
echo ========================================
echo 設定完成！
echo ========================================
echo.
echo 接下來請執行以下步驟：
echo.
echo 1. 啟動 XAMPP Control Panel
echo 2. 啟動 Apache 和 MySQL 服務
echo 3. 開啟 http://localhost/phpmyadmin
echo 4. 匯入資料庫：database/xampp_setup.sql
echo 5. 訪問系統：http://car-maintenance.local
echo.
echo 預設登入帳號：
echo 用戶名：admin
echo 密碼：password
echo.
echo 如需協助，請參考 README.md 文件
echo.
pause

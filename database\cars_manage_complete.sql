-- 汽車保養廠管理系統完整資料庫
-- 資料庫名稱: cars_manage

SET FOREIGN_KEY_CHECKS = 0;
DROP DATABASE IF EXISTS cars_manage;
CREATE DATABASE cars_manage CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cars_manage;

-- ================================
-- 1. 租戶管理相關表
-- ================================

-- 訂閱方案表
CREATE TABLE subscription_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '方案名稱',
    price DECIMAL(10,2) NOT NULL COMMENT '月費價格',
    duration_months INT NOT NULL DEFAULT 1 COMMENT '訂閱月數',
    max_users INT DEFAULT NULL COMMENT '最大用戶數',
    max_vehicles INT DEFAULT NULL COMMENT '最大車輛數',
    features JSON COMMENT '功能特色',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '訂閱方案表';

-- 租戶表
CREATE TABLE tenants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '保養廠名稱',
    subdomain VARCHAR(50) UNIQUE COMMENT '子網域',
    phone VARCHAR(20) COMMENT '聯絡電話',
    address TEXT COMMENT '地址',
    tax_id VARCHAR(20) COMMENT '統一編號',
    status ENUM('trial', 'active', 'suspended', 'cancelled') DEFAULT 'trial',
    trial_expires_at DATETIME COMMENT '試用到期時間',
    subscription_expires_at DATETIME COMMENT '訂閱到期時間',
    current_plan_id INT COMMENT '當前方案',
    auto_renew BOOLEAN DEFAULT FALSE COMMENT '自動續約',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (current_plan_id) REFERENCES subscription_plans(id)
) COMMENT '租戶表';

-- 用戶表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('owner', 'admin', 'manager', 'technician', 'staff') DEFAULT 'staff',
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tenant_email (tenant_id, email)
) COMMENT '用戶表';

-- ================================
-- 2. 支付相關表
-- ================================

-- 支付訂單表
CREATE TABLE payment_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    plan_id INT NOT NULL,
    order_no VARCHAR(50) UNIQUE NOT NULL COMMENT '訂單編號',
    amount DECIMAL(10,2) NOT NULL COMMENT '金額',
    status ENUM('pending', 'paid', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(20) COMMENT '支付方式',
    transaction_id VARCHAR(100) COMMENT '交易ID',
    payment_data JSON COMMENT '支付回傳資料',
    paid_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
) COMMENT '支付訂單表';

-- 訂閱記錄表
CREATE TABLE subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    plan_id INT NOT NULL,
    order_id INT,
    starts_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id),
    FOREIGN KEY (order_id) REFERENCES payment_orders(id)
) COMMENT '訂閱記錄表';

-- ================================
-- 3. 客戶管理相關表
-- ================================

-- 客戶表
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    customer_no VARCHAR(20) COMMENT '客戶編號',
    name VARCHAR(100) NOT NULL COMMENT '客戶姓名',
    phone VARCHAR(20),
    mobile VARCHAR(20),
    email VARCHAR(100),
    id_number VARCHAR(20) COMMENT '身分證字號',
    address TEXT COMMENT '地址',
    birthday DATE COMMENT '生日',
    gender ENUM('M', 'F') COMMENT '性別',
    notes TEXT COMMENT '備註',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant_phone (tenant_id, phone),
    INDEX idx_tenant_name (tenant_id, name)
) COMMENT '客戶表';

-- 車輛表
CREATE TABLE vehicles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    customer_id INT NOT NULL,
    license_plate VARCHAR(20) NOT NULL COMMENT '車牌號碼',
    brand VARCHAR(50) COMMENT '廠牌',
    model VARCHAR(50) COMMENT '型號',
    year INT COMMENT '年份',
    engine_no VARCHAR(50) COMMENT '引擎號碼',
    chassis_no VARCHAR(50) COMMENT '車身號碼',
    color VARCHAR(30) COMMENT '顏色',
    fuel_type ENUM('gasoline', 'diesel', 'hybrid', 'electric') COMMENT '燃料類型',
    mileage INT DEFAULT 0 COMMENT '里程數',
    notes TEXT COMMENT '備註',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tenant_plate (tenant_id, license_plate)
) COMMENT '車輛表';

-- ================================
-- 4. 工單管理相關表
-- ================================

-- 工單表
CREATE TABLE work_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    order_no VARCHAR(30) UNIQUE NOT NULL COMMENT '工單編號',
    customer_id INT NOT NULL,
    vehicle_id INT NOT NULL,
    technician_id INT COMMENT '負責技師',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    type ENUM('maintenance', 'repair', 'inspection', 'other') DEFAULT 'maintenance',
    description TEXT COMMENT '工作描述',
    customer_complaint TEXT COMMENT '客戶反映問題',
    diagnosis TEXT COMMENT '診斷結果',
    work_performed TEXT COMMENT '執行工作',
    mileage_in INT COMMENT '進廠里程',
    mileage_out INT COMMENT '出廠里程',
    estimated_hours DECIMAL(4,2) COMMENT '預估工時',
    actual_hours DECIMAL(4,2) COMMENT '實際工時',
    labor_cost DECIMAL(10,2) DEFAULT 0 COMMENT '工資費用',
    parts_cost DECIMAL(10,2) DEFAULT 0 COMMENT '零件費用',
    total_cost DECIMAL(10,2) DEFAULT 0 COMMENT '總費用',
    discount DECIMAL(10,2) DEFAULT 0 COMMENT '折扣',
    final_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最終金額',
    payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid',
    started_at DATETIME COMMENT '開始時間',
    completed_at DATETIME COMMENT '完成時間',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    FOREIGN KEY (technician_id) REFERENCES users(id),
    INDEX idx_tenant_status (tenant_id, status),
    INDEX idx_tenant_date (tenant_id, created_at)
) COMMENT '工單表';

-- ================================
-- 5. 零件庫存管理
-- ================================

-- 零件分類表
CREATE TABLE part_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    name VARCHAR(100) NOT NULL COMMENT '分類名稱',
    parent_id INT COMMENT '父分類ID',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES part_categories(id)
) COMMENT '零件分類表';

-- 零件表
CREATE TABLE parts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    category_id INT,
    part_no VARCHAR(50) NOT NULL COMMENT '零件編號',
    name VARCHAR(200) NOT NULL COMMENT '零件名稱',
    brand VARCHAR(100) COMMENT '品牌',
    specification TEXT COMMENT '規格說明',
    unit VARCHAR(20) DEFAULT '個' COMMENT '單位',
    cost_price DECIMAL(10,2) DEFAULT 0 COMMENT '成本價',
    selling_price DECIMAL(10,2) DEFAULT 0 COMMENT '售價',
    stock_quantity INT DEFAULT 0 COMMENT '庫存數量',
    min_stock INT DEFAULT 0 COMMENT '最低庫存',
    max_stock INT DEFAULT 0 COMMENT '最高庫存',
    location VARCHAR(100) COMMENT '存放位置',
    supplier VARCHAR(100) COMMENT '供應商',
    notes TEXT COMMENT '備註',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES part_categories(id),
    UNIQUE KEY unique_tenant_part_no (tenant_id, part_no),
    INDEX idx_tenant_name (tenant_id, name)
) COMMENT '零件表';

-- 工單零件明細表
CREATE TABLE work_order_parts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_order_id INT NOT NULL,
    part_id INT NOT NULL,
    quantity INT NOT NULL COMMENT '使用數量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '單價',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小計',
    notes VARCHAR(255) COMMENT '備註',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (part_id) REFERENCES parts(id)
) COMMENT '工單零件明細表';

-- ================================
-- 6. 預約管理
-- ================================

-- 預約表
CREATE TABLE appointments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    appointment_no VARCHAR(30) UNIQUE NOT NULL COMMENT '預約編號',
    customer_id INT NOT NULL,
    vehicle_id INT NOT NULL,
    technician_id INT COMMENT '指定技師',
    appointment_date DATE NOT NULL COMMENT '預約日期',
    appointment_time TIME NOT NULL COMMENT '預約時間',
    estimated_duration INT DEFAULT 60 COMMENT '預估時間(分鐘)',
    service_type VARCHAR(100) COMMENT '服務類型',
    description TEXT COMMENT '預約說明',
    customer_notes TEXT COMMENT '客戶備註',
    status ENUM('confirmed', 'completed', 'cancelled', 'no_show') DEFAULT 'confirmed',
    reminder_sent BOOLEAN DEFAULT FALSE COMMENT '是否已發送提醒',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
    FOREIGN KEY (technician_id) REFERENCES users(id),
    INDEX idx_tenant_date (tenant_id, appointment_date)
) COMMENT '預約表';

-- ================================
-- 7. 財務管理
-- ================================

-- 收款記錄表
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    work_order_id INT,
    payment_no VARCHAR(30) UNIQUE NOT NULL COMMENT '收款編號',
    amount DECIMAL(10,2) NOT NULL COMMENT '收款金額',
    payment_method ENUM('cash', 'card', 'transfer', 'check', 'other') DEFAULT 'cash',
    payment_date DATE NOT NULL COMMENT '收款日期',
    reference_no VARCHAR(100) COMMENT '參考號碼',
    notes TEXT COMMENT '備註',
    created_by INT NOT NULL COMMENT '建立人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (work_order_id) REFERENCES work_orders(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT '收款記錄表';

-- ================================
-- 8. 系統設定
-- ================================

-- 系統設定表
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tenant_key (tenant_id, setting_key)
) COMMENT '系統設定表';

-- 操作日誌表
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    user_id INT,
    action VARCHAR(100) NOT NULL COMMENT '操作動作',
    table_name VARCHAR(50) COMMENT '操作表名',
    record_id INT COMMENT '記錄ID',
    old_values JSON COMMENT '舊值',
    new_values JSON COMMENT '新值',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用戶代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_tenant_date (tenant_id, created_at),
    INDEX idx_tenant_user (tenant_id, user_id)
) COMMENT '操作日誌表';

-- ================================
-- 9. 初始化資料
-- ================================

-- 插入預設訂閱方案
INSERT INTO subscription_plans (name, price, duration_months, max_users, max_vehicles, features) VALUES
('基礎版', 2000.00, 1, 5, 100, '{"features": ["客戶管理", "車輛管理", "基本工單", "基本報表"]}'),
('專業版', 3500.00, 1, 15, 500, '{"features": ["完整功能", "庫存管理", "預約系統", "進階報表", "API接口"]}'),
('企業版', 5000.00, 1, NULL, NULL, '{"features": ["無限制用戶", "無限制車輛", "客製化功能", "專屬客服", "數據備份"]}');

-- 插入系統管理員租戶
INSERT INTO tenants (name, subdomain, status, current_plan_id) VALUES
('系統管理', 'admin', 'active', 3);

-- 插入系統管理員
INSERT INTO users (tenant_id, username, email, password, full_name, role) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系統管理員', 'owner');

-- 建立索引優化查詢效能
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_expires ON tenants(subscription_expires_at);
CREATE INDEX idx_users_tenant_role ON users(tenant_id, role);
CREATE INDEX idx_customers_tenant_active ON customers(tenant_id, is_active);
CREATE INDEX idx_vehicles_customer ON vehicles(customer_id);
CREATE INDEX idx_work_orders_customer ON work_orders(customer_id);
CREATE INDEX idx_work_orders_vehicle ON work_orders(vehicle_id);
CREATE INDEX idx_parts_tenant_active ON parts(tenant_id, is_active);

SET FOREIGN_KEY_CHECKS = 1;

-- 顯示建立完成訊息
SELECT 'Database cars_manage created successfully!' as message;
SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema = 'cars_manage';
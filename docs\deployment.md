# 部署指南

## Synology NAS 部署

### 系統需求
- Synology DSM 7.0+
- 至少 4GB RAM
- 20GB 可用空間

### 安裝步驟

#### 1. 安裝必要套件

1. **Web Station**
   - 開啟套件中心
   - 搜索並安裝 "Web Station"

2. **MariaDB 10**
   - 安裝 "MariaDB 10"
   - 設定 root 密碼

3. **PHP 8.3**
   - 安裝 "PHP 8.3"
   - 啟用必要擴展：
     - mysqli
     - pdo_mysql
     - curl
     - json
     - mbstring
     - openssl

#### 2. 配置 Web Station

1. **建立虛擬主機**
   ```
   主機名稱：car-maintenance
   連接埠：80 (HTTP), 443 (HTTPS)
   文件根目錄：/web/car-maintenance/public
   PHP 版本：8.3
   ```

2. **PHP 設定**
   ```ini
   memory_limit = 256M
   upload_max_filesize = 50M
   post_max_size = 50M
   max_execution_time = 300
   ```

#### 3. 資料庫設定

1. **建立資料庫**
   ```sql
   CREATE DATABASE car_maintenance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'car_maintenance'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON car_maintenance.* TO 'car_maintenance'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **匯入資料庫結構**
   ```bash
   mysql -u car_maintenance -p car_maintenance < database/schema.sql
   mysql -u car_maintenance -p car_maintenance < database/data.sql
   ```

#### 4. 檔案部署

1. **上傳檔案**
   - 將專案檔案上傳到 `/web/car-maintenance/`
   - 確保 `public` 目錄為 Web 根目錄

2. **設定權限**
   ```bash
   chmod -R 755 /web/car-maintenance/
   chmod -R 777 /web/car-maintenance/application/storage/
   chmod -R 777 /web/car-maintenance/public/uploads/
   ```

3. **配置檔案**
   ```bash
   cp application/conf/application.ini.example application/conf/application.ini
   ```

   編輯 `application.ini`：
   ```ini
   [product]
   database.host = localhost
   database.port = 3306
   database.username = car_maintenance
   database.password = your_password
   database.dbname = car_maintenance
   
   payment.ecpay.merchant_id = your_merchant_id
   payment.ecpay.hash_key = your_hash_key
   payment.ecpay.hash_iv = your_hash_iv
   payment.ecpay.test_mode = false
   ```

#### 5. SSL 憑證設定

1. **申請 Let's Encrypt 憑證**
   - 在 DSM 控制台 > 安全性 > 憑證
   - 新增憑證 > Let's Encrypt

2. **設定 HTTPS 重定向**
   - 在 Web Station 中啟用 HTTPS
   - 設定 HTTP 自動重定向到 HTTPS

#### 6. 定時任務設定

1. **建立 Cron 任務**
   ```bash
   # 編輯 crontab
   crontab -e
   
   # 添加以下任務
   # 每小時檢查到期租戶
   0 * * * * /usr/local/bin/php /web/car-maintenance/cron.php check-expiring
   
   # 每日處理自動續租
   0 2 * * * /usr/local/bin/php /web/car-maintenance/cron.php auto-renewal
   
   # 每日清理過期訂單
   0 3 * * * /usr/local/bin/php /web/car-maintenance/cron.php cleanup-orders
   ```

### 效能優化

#### 1. PHP 優化
```ini
; php.ini 設定
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
opcache.revalidate_freq=60
```

#### 2. 資料庫優化
```sql
-- MariaDB 設定
SET GLOBAL innodb_buffer_pool_size = 256M;
SET GLOBAL query_cache_size = 64M;
SET GLOBAL query_cache_type = 1;
```

#### 3. Web Server 優化
```nginx
# Nginx 設定（如果使用）
gzip on;
gzip_types text/plain text/css application/json application/javascript;
client_max_body_size 50M;
```

### 備份策略

#### 1. 資料庫備份
```bash
#!/bin/bash
# backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u car_maintenance -p car_maintenance > /backup/car_maintenance_$DATE.sql
```

#### 2. 檔案備份
```bash
#!/bin/bash
# backup_files.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backup/car_maintenance_files_$DATE.tar.gz /web/car-maintenance/
```

#### 3. 自動備份
```bash
# 每日凌晨 1 點備份
0 1 * * * /backup/backup_db.sh
0 1 * * * /backup/backup_files.sh
```

### 監控與維護

#### 1. 日誌監控
```bash
# 檢查錯誤日誌
tail -f /var/log/nginx/error.log
tail -f /var/log/php/error.log
```

#### 2. 效能監控
```bash
# 檢查系統資源
htop
iostat
```

#### 3. 資料庫監控
```sql
-- 檢查慢查詢
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Slow_queries';
```

### 故障排除

#### 1. 常見問題

**問題：500 錯誤**
```bash
# 檢查 PHP 錯誤日誌
tail -f /var/log/php/error.log

# 檢查檔案權限
ls -la /web/car-maintenance/
```

**問題：資料庫連接失敗**
```bash
# 檢查 MariaDB 狀態
systemctl status mariadb

# 測試連接
mysql -u car_maintenance -p
```

**問題：上傳檔案失敗**
```bash
# 檢查上傳目錄權限
chmod 777 /web/car-maintenance/public/uploads/

# 檢查 PHP 設定
php -i | grep upload
```

#### 2. 除錯模式

開發環境設定：
```ini
; application.ini
[development]
application.debug = true
application.showErrors = true
```

### 安全設定

#### 1. 防火牆設定
```bash
# 只開放必要端口
ufw allow 80
ufw allow 443
ufw allow 22
ufw enable
```

#### 2. 檔案權限
```bash
# 設定安全權限
find /web/car-maintenance/ -type f -exec chmod 644 {} \;
find /web/car-maintenance/ -type d -exec chmod 755 {} \;
chmod 777 /web/car-maintenance/application/storage/
chmod 777 /web/car-maintenance/public/uploads/
```

#### 3. 隱藏敏感資訊
```nginx
# Nginx 設定
location ~ /\. {
    deny all;
}

location ~ \.(ini|conf)$ {
    deny all;
}
```

### 更新流程

#### 1. 備份現有系統
```bash
./backup_db.sh
./backup_files.sh
```

#### 2. 部署新版本
```bash
# 下載新版本
git pull origin main

# 更新依賴
composer install --no-dev

# 執行資料庫遷移
php migrate.php
```

#### 3. 測試驗證
```bash
# 檢查系統狀態
curl -I https://your-domain.com/
```

### 聯絡支援

如遇到部署問題，請提供以下資訊：
- DSM 版本
- 錯誤日誌
- 系統配置
- 問題重現步驟

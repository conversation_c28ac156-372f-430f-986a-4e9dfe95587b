<?php
/**
 * 租戶設定控制器
 */
class SettingsController extends BaseController
{
    /**
     * 基本設定
     */
    public function indexAction()
    {
        if (!$this->tenant || !$this->user) {
            return $this->redirect('/auth/login');
        }
        
        if (!$this->hasPermission('setting.view')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleUpdateBasicSettings();
        }
        
        $tenantModel = new TenantModel();
        $tenant = $tenantModel->find($this->tenant['id']);
        
        $this->getView()->assign('tenant', $tenant);
        $this->getView()->assign('title', '基本設定');
    }

    /**
     * 處理基本設定更新
     */
    private function handleUpdateBasicSettings()
    {
        if (!$this->hasPermission('setting.edit')) {
            return $this->error('權限不足', 403);
        }
        
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'name' => 'required|max:100',
            'phone' => 'phone',
            'address' => 'max:255',
            'tax_id' => 'max:20'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        try {
            $tenantModel = new TenantModel();
            $oldData = $tenantModel->find($this->tenant['id']);
            
            $updateData = [
                'name' => $data['name'],
                'phone' => $data['phone'] ?? null,
                'address' => $data['address'] ?? null,
                'tax_id' => $data['tax_id'] ?? null
            ];
            
            $tenantModel->update($this->tenant['id'], $updateData);
            
            // 記錄操作日誌
            $this->logActivity('tenant_settings_update', 'tenants', $this->tenant['id'], $oldData, $updateData);
            
            if ($this->isAjax()) {
                return $this->success(null, '設定更新成功');
            }
            
            return $this->redirect('/tenant/settings?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 用戶管理
     */
    public function usersAction()
    {
        if (!$this->hasPermission('user.view')) {
            return $this->error('權限不足', 403);
        }
        
        $userModel = new UserModel();
        $users = $userModel->findAll(['is_active' => 1], 'created_at DESC');
        
        // 檢查用戶數量限制
        $tenantModel = new TenantModel();
        $limits = $tenantModel->checkLimits($this->tenant['id']);
        
        $this->getView()->assign('users', $users);
        $this->getView()->assign('limits', $limits);
        $this->getView()->assign('title', '用戶管理');
    }

    /**
     * 添加用戶
     */
    public function addUserAction()
    {
        if (!$this->hasPermission('user.create')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleAddUser();
        }
        
        $this->getView()->assign('title', '添加用戶');
    }

    /**
     * 處理添加用戶
     */
    private function handleAddUser()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'username' => 'required|min:3|max:50',
            'email' => 'required|email|max:100',
            'password' => 'required|min:6',
            'password_confirmation' => 'required|confirmed',
            'full_name' => 'required|max:100',
            'phone' => 'phone',
            'role' => 'required|in:admin,manager,technician,staff'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        // 檢查用戶數量限制
        $tenantModel = new TenantModel();
        $limits = $tenantModel->checkLimits($this->tenant['id']);
        if ($limits['users']['exceeded']) {
            if ($this->isAjax()) {
                return $this->error('已達到用戶數量上限');
            }
            $this->getView()->assign('error', '已達到用戶數量上限');
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        try {
            $userModel = new UserModel();
            
            // 檢查用戶名和郵箱是否可用
            if (!$userModel->isUsernameAvailable($data['username'])) {
                throw new Exception('用戶名已被使用');
            }
            
            if (!$userModel->isEmailAvailable($data['email'])) {
                throw new Exception('郵箱已被使用');
            }
            
            $userData = [
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => $data['password'],
                'full_name' => $data['full_name'],
                'phone' => $data['phone'] ?? null,
                'role' => $data['role']
            ];
            
            $user = $userModel->create($userData);
            
            // 記錄操作日誌
            $this->logActivity('user_create', 'users', $user['id'], null, $userData);
            
            if ($this->isAjax()) {
                return $this->success(['user_id' => $user['id']], '用戶創建成功');
            }
            
            return $this->redirect('/tenant/settings/users?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('創建失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '創建失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 編輯用戶
     */
    public function editUserAction()
    {
        if (!$this->hasPermission('user.edit')) {
            return $this->error('權限不足', 403);
        }
        
        $userId = $this->getParam('id');
        if (!$userId) {
            return $this->error('用戶ID不能為空', 400);
        }
        
        $userModel = new UserModel();
        $user = $userModel->find($userId);
        if (!$user) {
            return $this->error('用戶不存在', 404);
        }
        
        if ($this->isPost()) {
            return $this->handleEditUser($userId);
        }
        
        $this->getView()->assign('user', $user);
        $this->getView()->assign('title', '編輯用戶');
    }

    /**
     * 處理編輯用戶
     */
    private function handleEditUser($userId)
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'username' => 'required|min:3|max:50',
            'email' => 'required|email|max:100',
            'full_name' => 'required|max:100',
            'phone' => 'phone',
            'role' => 'required|in:admin,manager,technician,staff'
        ]);
        
        // 如果有密碼，驗證密碼
        if (!empty($data['password'])) {
            $passwordValidation = $this->validate($data, [
                'password' => 'required|min:6',
                'password_confirmation' => 'required|confirmed'
            ]);
            
            if (!$passwordValidation['valid']) {
                $validation['valid'] = false;
                $validation['errors'] = array_merge($validation['errors'], $passwordValidation['errors']);
            }
        }
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        try {
            $userModel = new UserModel();
            $oldData = $userModel->find($userId);
            
            // 檢查用戶名和郵箱是否可用
            if (!$userModel->isUsernameAvailable($data['username'], $userId)) {
                throw new Exception('用戶名已被使用');
            }
            
            if (!$userModel->isEmailAvailable($data['email'], $userId)) {
                throw new Exception('郵箱已被使用');
            }
            
            $updateData = [
                'username' => $data['username'],
                'email' => $data['email'],
                'full_name' => $data['full_name'],
                'phone' => $data['phone'] ?? null,
                'role' => $data['role']
            ];
            
            // 如果有密碼，添加到更新數據中
            if (!empty($data['password'])) {
                $updateData['password'] = $data['password'];
            }
            
            $userModel->update($userId, $updateData);
            
            // 記錄操作日誌
            $this->logActivity('user_update', 'users', $userId, $oldData, $updateData);
            
            if ($this->isAjax()) {
                return $this->success(null, '用戶更新成功');
            }
            
            return $this->redirect('/tenant/settings/users?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 停用/啟用用戶
     */
    public function toggleUserAction()
    {
        if (!$this->hasPermission('user.edit')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $userId = $this->getPost('user_id');
        $action = $this->getPost('action'); // 'activate' or 'deactivate'
        
        if (!$userId || !in_array($action, ['activate', 'deactivate'])) {
            return $this->error('參數錯誤', 400);
        }
        
        // 不能停用自己
        if ($userId == $this->user['id']) {
            return $this->error('不能停用自己的帳號');
        }
        
        try {
            $userModel = new UserModel();
            $user = $userModel->find($userId);
            if (!$user) {
                return $this->error('用戶不存在', 404);
            }
            
            if ($action === 'activate') {
                $userModel->activate($userId);
                $message = '用戶已啟用';
            } else {
                $userModel->deactivate($userId);
                $message = '用戶已停用';
            }
            
            // 記錄操作日誌
            $this->logActivity('user_' . $action, 'users', $userId);
            
            return $this->success(null, $message);
            
        } catch (Exception $e) {
            return $this->error('操作失敗：' . $e->getMessage());
        }
    }

    /**
     * 系統設定
     */
    public function systemAction()
    {
        if (!$this->hasPermission('setting.view')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleUpdateSystemSettings();
        }
        
        $settingModel = new SystemSettingModel();
        $settings = $settingModel->getTenantSettings($this->tenant['id']);
        
        $this->getView()->assign('settings', $settings);
        $this->getView()->assign('title', '系統設定');
    }

    /**
     * 處理系統設定更新
     */
    private function handleUpdateSystemSettings()
    {
        if (!$this->hasPermission('setting.edit')) {
            return $this->error('權限不足', 403);
        }
        
        $data = $this->getPost();
        
        try {
            $settingModel = new SystemSettingModel();
            
            foreach ($data as $key => $value) {
                $settingModel->setSetting($this->tenant['id'], $key, $value);
            }
            
            // 記錄操作日誌
            $this->logActivity('system_settings_update', 'system_settings', null, null, $data);
            
            if ($this->isAjax()) {
                return $this->success(null, '設定更新成功');
            }
            
            return $this->redirect('/tenant/settings/system?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }
}

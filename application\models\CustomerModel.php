<?php
/**
 * 客戶模型
 */
class CustomerModel extends BaseModel
{
    protected $table = 'customers';
    
    protected $fillable = [
        'customer_no', 'name', 'phone', 'mobile', 'email', 'id_number',
        'address', 'birthday', 'gender', 'notes', 'is_active'
    ];

    /**
     * 搜索客戶
     */
    public function search($query, $limit = 10)
    {
        $sql = "
            SELECT c.*, COUNT(v.id) as vehicle_count
            FROM customers c
            LEFT JOIN vehicles v ON c.id = v.customer_id AND v.is_active = 1
            WHERE c.tenant_id = ?
            AND c.is_active = 1
            AND (
                c.name LIKE ? OR
                c.phone LIKE ? OR
                c.mobile LIKE ? OR
                c.customer_no LIKE ?
            )
            GROUP BY c.id
            ORDER BY c.name ASC
            LIMIT ?
        ";
        
        $searchTerm = "%{$query}%";
        $tenantId = $this->getCurrentTenantId();
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取客戶詳情（包含車輛）
     */
    public function getCustomerDetails($customerId)
    {
        $customer = $this->find($customerId);
        if (!$customer) {
            return null;
        }
        
        // 獲取客戶車輛
        $vehicleModel = new VehicleModel();
        $customer['vehicles'] = $vehicleModel->findAll(['customer_id' => $customerId, 'is_active' => 1]);
        
        // 獲取最近工單
        $workOrderModel = new WorkOrderModel();
        $customer['recent_orders'] = $workOrderModel->getCustomerRecentOrders($customerId, 5);
        
        return $customer;
    }

    /**
     * 生成客戶編號
     */
    public function generateCustomerNo()
    {
        $tenantId = $this->getCurrentTenantId();
        $prefix = 'C' . date('Ym');
        
        // 獲取當月最大編號
        $stmt = $this->db->prepare("
            SELECT customer_no 
            FROM customers 
            WHERE tenant_id = ? 
            AND customer_no LIKE ? 
            ORDER BY customer_no DESC 
            LIMIT 1
        ");
        $stmt->execute([$tenantId, $prefix . '%']);
        $lastCustomer = $stmt->fetch();
        
        if ($lastCustomer) {
            $lastNo = intval(substr($lastCustomer['customer_no'], -4));
            $newNo = $lastNo + 1;
        } else {
            $newNo = 1;
        }
        
        return $prefix . str_pad($newNo, 4, '0', STR_PAD_LEFT);
    }

    /**
     * 創建客戶
     */
    public function create($data)
    {
        // 自動生成客戶編號
        if (empty($data['customer_no'])) {
            $data['customer_no'] = $this->generateCustomerNo();
        }
        
        return parent::create($data);
    }

    /**
     * 獲取客戶統計
     */
    public function getStats()
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stats = [];
        
        // 總客戶數
        $stats['total'] = $this->count(['is_active' => 1]);
        
        // 本月新增客戶
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM customers 
            WHERE tenant_id = ? 
            AND YEAR(created_at) = YEAR(NOW()) 
            AND MONTH(created_at) = MONTH(NOW())
        ");
        $stmt->execute([$tenantId]);
        $stats['monthly_new'] = $stmt->fetch()['count'];
        
        // 性別分布
        $stmt = $this->db->prepare("
            SELECT gender, COUNT(*) as count 
            FROM customers 
            WHERE tenant_id = ? AND is_active = 1 AND gender IS NOT NULL
            GROUP BY gender
        ");
        $stmt->execute([$tenantId]);
        $genderStats = $stmt->fetchAll();
        $stats['by_gender'] = [];
        foreach ($genderStats as $stat) {
            $stats['by_gender'][$stat['gender']] = $stat['count'];
        }
        
        // 年齡分布
        $stmt = $this->db->prepare("
            SELECT 
                CASE 
                    WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) < 30 THEN 'under_30'
                    WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) < 50 THEN '30_to_50'
                    WHEN TIMESTAMPDIFF(YEAR, birthday, CURDATE()) < 70 THEN '50_to_70'
                    ELSE 'over_70'
                END as age_group,
                COUNT(*) as count
            FROM customers 
            WHERE tenant_id = ? AND is_active = 1 AND birthday IS NOT NULL
            GROUP BY age_group
        ");
        $stmt->execute([$tenantId]);
        $ageStats = $stmt->fetchAll();
        $stats['by_age'] = [];
        foreach ($ageStats as $stat) {
            $stats['by_age'][$stat['age_group']] = $stat['count'];
        }
        
        return $stats;
    }

    /**
     * 獲取客戶的消費記錄
     */
    public function getCustomerSpending($customerId, $months = 12)
    {
        $stmt = $this->db->prepare("
            SELECT 
                DATE_FORMAT(wo.completed_at, '%Y-%m') as month,
                COUNT(*) as order_count,
                SUM(wo.final_amount) as total_amount
            FROM work_orders wo
            WHERE wo.customer_id = ?
            AND wo.status = 'completed'
            AND wo.payment_status = 'paid'
            AND wo.completed_at >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            GROUP BY DATE_FORMAT(wo.completed_at, '%Y-%m')
            ORDER BY month DESC
        ");
        $stmt->execute([$customerId, $months]);
        
        return $stmt->fetchAll();
    }

    /**
     * 檢查客戶是否可以刪除
     */
    public function canDelete($customerId)
    {
        // 檢查是否有關聯的車輛
        $vehicleCount = $this->db->prepare("SELECT COUNT(*) as count FROM vehicles WHERE customer_id = ?");
        $vehicleCount->execute([$customerId]);
        if ($vehicleCount->fetch()['count'] > 0) {
            return false;
        }
        
        // 檢查是否有關聯的工單
        $orderCount = $this->db->prepare("SELECT COUNT(*) as count FROM work_orders WHERE customer_id = ?");
        $orderCount->execute([$customerId]);
        if ($orderCount->fetch()['count'] > 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 合併客戶
     */
    public function mergeCustomers($keepCustomerId, $mergeCustomerId)
    {
        $this->beginTransaction();
        
        try {
            // 更新車輛關聯
            $stmt = $this->db->prepare("UPDATE vehicles SET customer_id = ? WHERE customer_id = ?");
            $stmt->execute([$keepCustomerId, $mergeCustomerId]);
            
            // 更新工單關聯
            $stmt = $this->db->prepare("UPDATE work_orders SET customer_id = ? WHERE customer_id = ?");
            $stmt->execute([$keepCustomerId, $mergeCustomerId]);
            
            // 更新預約關聯
            $stmt = $this->db->prepare("UPDATE appointments SET customer_id = ? WHERE customer_id = ?");
            $stmt->execute([$keepCustomerId, $mergeCustomerId]);
            
            // 刪除被合併的客戶
            $this->delete($mergeCustomerId);
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
}

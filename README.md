# 汽車保養廠管理系統

基於 PHP Yaf 框架開發的多租戶汽車保養廠管理系統，支援訂閱制 SaaS 模式。

## 系統特色

- **多租戶架構**：支援多個保養廠獨立使用，數據完全隔離
- **訂閱制模式**：整合台灣主流支付系統（綠界科技、藍新金流）
- **完整業務流程**：從客戶預約到工單完成的全流程管理
- **庫存管理**：零件分類、庫存控制、自動警示
- **權限控制**：細粒度權限管理，支援多種角色
- **響應式設計**：支援桌面和行動裝置

## 技術架構

### 後端技術
- **框架**：PHP 8.3 + Yaf 3.x
- **資料庫**：MariaDB 10.x
- **快取**：Redis（可選）
- **支付**：綠界科技 ECPay API

### 前端技術
- **框架**：Vue.js 3.x
- **UI 庫**：Element Plus / Bootstrap 5
- **構建工具**：Vite

## 功能模組

### ✅ 已完成功能

#### 1. 系統核心架構
- [x] Yaf 框架配置
- [x] 多租戶中間件
- [x] 資料庫連接池
- [x] 基礎模型類別
- [x] 錯誤處理機制

#### 2. 用戶認證與權限
- [x] 用戶註冊/登入
- [x] 角色權限管理
- [x] 多租戶數據隔離
- [x] Session 管理
- [x] 密碼加密

#### 3. 租戶管理
- [x] 租戶註冊流程
- [x] 訂閱方案管理
- [x] 試用期控制
- [x] 租戶設定管理

#### 4. 支付系統
- [x] 綠界科技整合
- [x] 訂單管理
- [x] 自動續租機制
- [x] 支付通知處理
- [x] 退款處理

#### 5. 客戶與車輛管理
- [x] 客戶資料管理
- [x] 車輛檔案管理
- [x] 客戶車輛關聯
- [x] 搜索與篩選
- [x] 資料匯出

#### 6. 工單系統
- [x] 工單建立與編輯
- [x] 狀態流程管理
- [x] 零件使用記錄
- [x] 費用計算
- [x] 工單列印

#### 7. 零件庫存管理
- [x] 零件分類管理
- [x] 庫存控制
- [x] 進出貨記錄
- [x] 低庫存警示
- [x] 零件搜索

#### 8. 預約系統
- [x] 線上預約功能
- [x] 時間衝突檢查
- [x] 技師排程
- [x] 預約狀態管理
- [x] 預約日曆

#### 9. 儀表板
- [x] 統計數據展示
- [x] 快速操作入口
- [x] 通知中心
- [x] 全域搜索

### 🚧 待開發功能

#### 10. 庫存管理系統
- [ ] 供應商管理
- [ ] 採購訂單
- [ ] 庫存盤點
- [ ] 成本分析

#### 11. 列印功能模組
- [ ] 工單列印範本
- [ ] 發票列印
- [ ] 報價單列印
- [ ] 列印記錄

#### 12. 財務管理
- [ ] 收款記錄
- [ ] 帳務追蹤
- [ ] 財務報表
- [ ] 稅務處理

#### 13. 前端界面開發
- [ ] Vue.js 組件開發
- [ ] 響應式界面
- [ ] 用戶體驗優化
- [ ] 行動端適配

#### 14. 系統測試與部署
- [ ] 單元測試
- [ ] 整合測試
- [ ] 安全性測試
- [ ] Synology NAS 部署

## 安裝與配置

### 系統需求
- PHP 8.3+
- MariaDB 10.x+
- Nginx/Apache
- Redis（可選）

### 安裝步驟

1. **克隆專案**
```bash
git clone [repository-url]
cd car-maintenance-system
```

2. **安裝依賴**
```bash
composer install
npm install
```

3. **配置資料庫**
```bash
# 複製配置文件
cp application/conf/application.ini.example application/conf/application.ini

# 編輯資料庫配置
vim application/conf/application.ini
```

4. **建立資料庫**
```bash
mysql -u root -p < database/schema.sql
mysql -u root -p < database/data.sql
```

5. **設定權限**
```bash
chmod -R 755 public/
chmod -R 777 application/storage/
```

6. **啟動服務**
```bash
# 開發環境
php -S localhost:8000 -t public/

# 生產環境（Nginx 配置）
# 參考 docs/nginx.conf
```

## 目錄結構

```
car-maintenance-system/
├── application/           # 應用程式核心
│   ├── controllers/      # 控制器
│   ├── models/          # 模型
│   ├── views/           # 視圖
│   ├── library/         # 類別庫
│   ├── modules/         # 模組
│   ├── conf/           # 配置文件
│   └── cron/           # 定時任務
├── public/              # 公開目錄
│   ├── index.php       # 入口文件
│   ├── assets/         # 靜態資源
│   └── uploads/        # 上傳文件
├── database/           # 資料庫文件
├── docs/              # 文檔
├── tests/             # 測試文件
└── vendor/            # Composer 依賴
```

## API 文檔

### 認證 API
- `POST /auth/login` - 用戶登入
- `POST /auth/logout` - 用戶登出
- `POST /auth/register` - 用戶註冊

### 客戶管理 API
- `GET /api/customers` - 獲取客戶列表
- `POST /api/customers` - 新增客戶
- `PUT /api/customers/{id}` - 更新客戶
- `DELETE /api/customers/{id}` - 刪除客戶

### 工單管理 API
- `GET /api/work-orders` - 獲取工單列表
- `POST /api/work-orders` - 新增工單
- `PUT /api/work-orders/{id}` - 更新工單
- `PUT /api/work-orders/{id}/status` - 更新工單狀態

## 部署指南

### Synology NAS 部署

1. **安裝 Web Station**
2. **配置 PHP 8.3**
3. **設定虛擬主機**
4. **配置資料庫**
5. **設定 SSL 憑證**

詳細部署步驟請參考 `docs/deployment.md`

## 開發指南

### 編碼規範
- 遵循 PSR-12 編碼標準
- 使用中文註釋
- 變數命名使用駝峰式

### 資料庫設計
- 所有表格包含 `tenant_id` 欄位
- 使用軟刪除機制
- 建立適當的索引

### 安全考量
- 所有輸入進行驗證
- 使用參數化查詢
- 實施 CSRF 保護
- 定期更新依賴

## 授權條款

本專案採用 MIT 授權條款，詳見 LICENSE 文件。

## 聯絡資訊

如有問題或建議，請聯絡開發團隊。

---

**版本**：v1.0.0  
**最後更新**：2025-01-15

<?php
/**
 * 綠界科技支付服務
 */
class EcpayService
{
    private $config;
    private $merchantId;
    private $hashKey;
    private $hashIv;
    private $testMode;

    public function __construct()
    {
        $this->config = Yaf_Registry::get('config');
        $this->merchantId = $this->config->payment->ecpay->merchant_id;
        $this->hashKey = $this->config->payment->ecpay->hash_key;
        $this->hashIv = $this->config->payment->ecpay->hash_iv;
        $this->testMode = $this->config->payment->ecpay->test_mode;
    }

    /**
     * 生成支付表單
     */
    public function generatePaymentForm($order)
    {
        $params = [
            'MerchantID' => $this->merchantId,
            'MerchantTradeNo' => $order['order_no'],
            'MerchantTradeDate' => date('Y/m/d H:i:s'),
            'PaymentType' => 'aio',
            'TotalAmount' => (int)$order['amount'],
            'TradeDesc' => '汽車保養廠管理系統訂閱',
            'ItemName' => $order['plan_name'] . ' 訂閱方案',
            'ReturnURL' => $this->getNotifyUrl(),
            'ClientBackURL' => $this->getReturnUrl($order['order_no']),
            'ChoosePayment' => 'ALL',
            'EncryptType' => 1,
        ];

        // 生成檢查碼
        $params['CheckMacValue'] = $this->generateCheckMacValue($params);

        // 生成表單HTML
        $actionUrl = $this->getActionUrl();
        $formHtml = $this->buildForm($actionUrl, $params);

        return [
            'action_url' => $actionUrl,
            'params' => $params,
            'form_html' => $formHtml
        ];
    }

    /**
     * 處理支付通知
     */
    public function handleNotify($postData)
    {
        try {
            // 驗證檢查碼
            if (!$this->verifyCheckMacValue($postData)) {
                throw new Exception('檢查碼驗證失敗');
            }

            $orderNo = $postData['MerchantTradeNo'];
            $tradeNo = $postData['TradeNo'];
            $paymentDate = $postData['PaymentDate'];
            $paymentType = $postData['PaymentType'];
            $rtnCode = $postData['RtnCode'];
            $rtnMsg = $postData['RtnMsg'];

            $paymentOrderModel = new PaymentOrderModel();
            $order = $paymentOrderModel->findByOrderNo($orderNo);

            if (!$order) {
                throw new Exception('訂單不存在');
            }

            if ($rtnCode == '1') {
                // 支付成功
                $paymentData = [
                    'trade_no' => $tradeNo,
                    'payment_date' => $paymentDate,
                    'payment_type' => $paymentType,
                    'ecpay_response' => $postData
                ];

                $paymentOrderModel->handlePaymentSuccess($orderNo, $tradeNo, $paymentData);

                // 記錄成功日誌
                $this->logPayment($orderNo, 'success', $paymentData);

                return ['success' => true, 'message' => '支付成功'];

            } else {
                // 支付失敗
                $paymentOrderModel->handlePaymentFailure($orderNo, $rtnMsg);

                // 記錄失敗日誌
                $this->logPayment($orderNo, 'failed', ['error' => $rtnMsg, 'response' => $postData]);

                return ['success' => false, 'message' => $rtnMsg];
            }

        } catch (Exception $e) {
            error_log('ECPay notify error: ' . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 生成檢查碼
     */
    private function generateCheckMacValue($params)
    {
        // 移除 CheckMacValue
        unset($params['CheckMacValue']);

        // 按照 key 排序
        ksort($params);

        // 組合字串
        $checkStr = 'HashKey=' . $this->hashKey;
        foreach ($params as $key => $value) {
            $checkStr .= '&' . $key . '=' . $value;
        }
        $checkStr .= '&HashIV=' . $this->hashIv;

        // URL encode
        $checkStr = urlencode($checkStr);

        // 轉小寫
        $checkStr = strtolower($checkStr);

        // 特殊字符處理
        $checkStr = str_replace(['%2d', '%5f', '%2e', '%21', '%2a', '%28', '%29'],
                               ['-', '_', '.', '!', '*', '(', ')'], $checkStr);

        // 產生檢查碼
        return strtoupper(hash('sha256', $checkStr));
    }

    /**
     * 驗證檢查碼
     */
    private function verifyCheckMacValue($params)
    {
        $receivedCheckMac = $params['CheckMacValue'];
        $calculatedCheckMac = $this->generateCheckMacValue($params);

        return hash_equals($receivedCheckMac, $calculatedCheckMac);
    }

    /**
     * 獲取 Action URL
     */
    private function getActionUrl()
    {
        return $this->testMode ?
            'https://payment-stage.ecpay.com.tw/Cashier/AioCheckOut/V5' :
            'https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5';
    }

    /**
     * 獲取通知 URL
     */
    private function getNotifyUrl()
    {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . '/payment/ecpay-notify';
    }

    /**
     * 獲取返回 URL
     */
    private function getReturnUrl($orderNo)
    {
        $baseUrl = $this->getBaseUrl();
        return $baseUrl . '/payment/return?order_no=' . $orderNo;
    }

    /**
     * 獲取基礎 URL
     */
    private function getBaseUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }

    /**
     * 建立表單 HTML
     */
    private function buildForm($actionUrl, $params)
    {
        $html = '<form id="ecpay-form" method="post" action="' . $actionUrl . '">';

        foreach ($params as $key => $value) {
            $html .= '<input type="hidden" name="' . $key . '" value="' . htmlspecialchars($value) . '">';
        }

        $html .= '<button type="submit" class="btn btn-primary">前往付款</button>';
        $html .= '</form>';

        // 自動提交的 JavaScript
        $html .= '<script>document.getElementById("ecpay-form").submit();</script>';

        return $html;
    }

    /**
     * 記錄支付日誌
     */
    private function logPayment($orderNo, $status, $data)
    {
        $logData = [
            'order_no' => $orderNo,
            'status' => $status,
            'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        error_log('ECPay Payment Log: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 舊版相容方法
     */
    public function createPayment($order)
    {
        return $this->generatePaymentForm($order);
    }

    /**
     * 舊版相容方法
     */
    public function verifyCallback($postData)
    {
        return $this->handleNotify($postData);
    }
}
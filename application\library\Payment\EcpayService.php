<?php
class EcpayService {
    
    public function createPayment($order) {
        $ecpay = new ECPay_AllInOne();
        
        // 基本設定
        $ecpay->ServiceURL = "https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5";
        $ecpay->HashKey = 'YOUR_HASH_KEY';
        $ecpay->HashIV = 'YOUR_HASH_IV';
        $ecpay->MerchantID = 'YOUR_MERCHANT_ID';
        
        // 訂單資訊
        $ecpay->Send['MerchantTradeNo'] = $order['order_no'];
        $ecpay->Send['TotalAmount'] = $order['amount'];
        $ecpay->Send['TradeDesc'] = '系統續租費用';
        $ecpay->Send['ItemName'] = $order['plan_name'];
        $ecpay->Send['ReturnURL'] = 'https://yourdomain.com/payment/callback';
        $ecpay->Send['ClientBackURL'] = 'https://yourdomain.com/dashboard';
        
        return $ecpay->CheckOutString();
    }
    
    public function verifyCallback($postData) {
        // 驗證回傳資料
        // 更新訂單狀態
        // 自動續租處理
    }
}
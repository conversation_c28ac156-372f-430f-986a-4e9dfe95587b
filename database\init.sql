-- 先建立最核心的表
CREATE TABLE tenants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    subdomain VARCHAR(50) UNIQUE,
    status ENUM('active', 'suspended', 'trial') DEFAULT 'trial',
    trial_expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    email VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('owner', 'admin', 'staff') DEFAULT 'staff',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);
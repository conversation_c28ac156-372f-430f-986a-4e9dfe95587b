<?php
class AutoRenewalCron {
    
    public function checkExpiring() {
        // 檢查即將到期的租戶（提前7天通知）
        $expiring = $this->getExpiringTenants();
        
        foreach ($expiring as $tenant) {
            $this->sendRenewalNotice($tenant);
        }
    }
    
    public function processAutoRenewal() {
        // 處理自動續租
        $autoRenewTenants = $this->getAutoRenewTenants();
        
        foreach ($autoRenewTenants as $tenant) {
            if ($tenant['auto_renew'] && $tenant['has_payment_method']) {
                $this->processRenewal($tenant);
            }
        }
    }
}
<?php
/**
 * 自動續租 Cron 任務
 */
class AutoRenewalCron
{
    private $db;
    private $config;

    public function __construct()
    {
        // 初始化資料庫連接
        $config = parse_ini_file(__DIR__ . '/../conf/application.ini', true);
        $dbConfig = $config['product']['database'] ?? $config['development']['database'];

        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};charset={$dbConfig['charset']}";
        $this->db = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        $this->config = $config;
    }

    /**
     * 檢查即將到期的租戶
     */
    public function checkExpiring()
    {
        echo "開始檢查即將到期的租戶...\n";

        try {
            // 檢查即將到期的租戶（提前7天、3天、1天通知）
            $this->sendExpiringNotifications(7);
            $this->sendExpiringNotifications(3);
            $this->sendExpiringNotifications(1);

            echo "到期檢查完成\n";

        } catch (Exception $e) {
            echo "到期檢查失敗: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 處理自動續租
     */
    public function processAutoRenewal()
    {
        echo "開始處理自動續租...\n";

        try {
            $autoRenewTenants = $this->getAutoRenewTenants();
            $processedCount = 0;
            $failedCount = 0;

            foreach ($autoRenewTenants as $tenant) {
                try {
                    $this->processRenewal($tenant);
                    $processedCount++;
                    echo "租戶 {$tenant['name']} 自動續租成功\n";

                } catch (Exception $e) {
                    $failedCount++;
                    echo "租戶 {$tenant['name']} 自動續租失敗: " . $e->getMessage() . "\n";

                    // 記錄失敗日誌
                    $this->logRenewalFailure($tenant['id'], $e->getMessage());
                }
            }

            echo "自動續租處理完成，成功: {$processedCount}，失敗: {$failedCount}\n";

        } catch (Exception $e) {
            echo "自動續租處理失敗: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 暫停過期租戶
     */
    public function suspendExpiredTenants()
    {
        echo "開始暫停過期租戶...\n";

        try {
            $expiredTenants = $this->getExpiredTenants();
            $suspendedCount = 0;

            foreach ($expiredTenants as $tenant) {
                $this->suspendTenant($tenant['id']);
                $suspendedCount++;
                echo "租戶 {$tenant['name']} 已暫停\n";
            }

            echo "過期租戶暫停完成，共暫停: {$suspendedCount}\n";

        } catch (Exception $e) {
            echo "暫停過期租戶失敗: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 清理過期訂單
     */
    public function cleanupExpiredOrders()
    {
        echo "開始清理過期訂單...\n";

        try {
            $stmt = $this->db->prepare("
                UPDATE payment_orders
                SET status = 'cancelled'
                WHERE status = 'pending'
                AND created_at < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            ");
            $stmt->execute();

            $cancelledCount = $stmt->rowCount();
            echo "清理過期訂單完成，共取消: {$cancelledCount}\n";

        } catch (Exception $e) {
            echo "清理過期訂單失敗: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 發送到期通知
     */
    private function sendExpiringNotifications($days)
    {
        $stmt = $this->db->prepare("
            SELECT t.*, sp.name as plan_name, sp.price
            FROM tenants t
            LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id
            WHERE t.status = 'active'
            AND t.subscription_expires_at IS NOT NULL
            AND DATE(t.subscription_expires_at) = DATE_ADD(CURDATE(), INTERVAL ? DAY)
        ");
        $stmt->execute([$days]);
        $tenants = $stmt->fetchAll();

        foreach ($tenants as $tenant) {
            $this->sendRenewalNotice($tenant, $days);
        }

        echo "發送 {$days} 天到期通知，共 " . count($tenants) . " 個租戶\n";
    }

    /**
     * 獲取需要自動續租的租戶
     */
    private function getAutoRenewTenants()
    {
        $stmt = $this->db->prepare("
            SELECT t.*, sp.name as plan_name, sp.price
            FROM tenants t
            LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id
            WHERE t.status = 'active'
            AND t.auto_renew = 1
            AND t.subscription_expires_at IS NOT NULL
            AND t.subscription_expires_at <= DATE_ADD(NOW(), INTERVAL 3 DAY)
            AND t.subscription_expires_at > NOW()
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * 獲取已過期的租戶
     */
    private function getExpiredTenants()
    {
        $stmt = $this->db->prepare("
            SELECT t.*, sp.name as plan_name
            FROM tenants t
            LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id
            WHERE t.status = 'active'
            AND (
                (t.trial_expires_at IS NOT NULL AND t.trial_expires_at < NOW()) OR
                (t.subscription_expires_at IS NOT NULL AND t.subscription_expires_at < NOW())
            )
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * 處理單個租戶的續租
     */
    private function processRenewal($tenant)
    {
        // 創建續租訂單
        $orderData = [
            'tenant_id' => $tenant['id'],
            'plan_id' => $tenant['current_plan_id'],
            'amount' => $tenant['price'],
            'auto_renewal' => true
        ];

        $orderNo = $this->createRenewalOrder($orderData);

        // 這裡應該調用支付API進行自動扣款
        // 由於是示例，我們假設支付成功
        $this->processRenewalPayment($orderNo, $tenant);
    }

    /**
     * 創建續租訂單
     */
    private function createRenewalOrder($data)
    {
        $orderNo = 'AR' . date('YmdHis') . str_pad($data['tenant_id'], 4, '0', STR_PAD_LEFT);

        $stmt = $this->db->prepare("
            INSERT INTO payment_orders (tenant_id, plan_id, order_no, amount, status, created_at)
            VALUES (?, ?, ?, ?, 'pending', NOW())
        ");
        $stmt->execute([$data['tenant_id'], $data['plan_id'], $orderNo, $data['amount']]);

        return $orderNo;
    }

    /**
     * 處理續租支付
     */
    private function processRenewalPayment($orderNo, $tenant)
    {
        // 模擬支付成功
        $transactionId = 'TXN' . time() . rand(1000, 9999);

        $this->db->beginTransaction();

        try {
            // 更新訂單狀態
            $stmt = $this->db->prepare("
                UPDATE payment_orders
                SET status = 'paid', transaction_id = ?, paid_at = NOW()
                WHERE order_no = ?
            ");
            $stmt->execute([$transactionId, $orderNo]);

            // 延長訂閱
            $newExpiresAt = date('Y-m-d H:i:s', strtotime($tenant['subscription_expires_at'] . ' +1 month'));
            $stmt = $this->db->prepare("
                UPDATE tenants
                SET subscription_expires_at = ?
                WHERE id = ?
            ");
            $stmt->execute([$newExpiresAt, $tenant['id']]);

            // 創建訂閱記錄
            $stmt = $this->db->prepare("
                INSERT INTO subscriptions (tenant_id, plan_id, starts_at, expires_at, status, created_at)
                VALUES (?, ?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([
                $tenant['id'],
                $tenant['current_plan_id'],
                $tenant['subscription_expires_at'],
                $newExpiresAt
            ]);

            $this->db->commit();

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 暫停租戶
     */
    private function suspendTenant($tenantId)
    {
        $stmt = $this->db->prepare("
            UPDATE tenants
            SET status = 'suspended'
            WHERE id = ?
        ");
        $stmt->execute([$tenantId]);
    }

    /**
     * 發送續租通知
     */
    private function sendRenewalNotice($tenant, $days)
    {
        // 這裡應該實現郵件或簡訊通知
        echo "發送續租通知給租戶 {$tenant['name']}，{$days} 天後到期\n";
    }

    /**
     * 記錄續租失敗日誌
     */
    private function logRenewalFailure($tenantId, $error)
    {
        $stmt = $this->db->prepare("
            INSERT INTO activity_logs (tenant_id, action, table_name, record_id, new_values, created_at)
            VALUES (?, 'auto_renewal_failed', 'tenants', ?, ?, NOW())
        ");
        $stmt->execute([$tenantId, $tenantId, json_encode(['error' => $error])]);
    }
}
<?php
/**
 * 支付訂單模型
 */
class PaymentOrderModel extends BaseModel
{
    protected $table = 'payment_orders';
    protected $tenantField = null; // 支付訂單不需要租戶過濾
    
    protected $fillable = [
        'tenant_id', 'plan_id', 'order_no', 'amount', 'status',
        'payment_method', 'transaction_id', 'payment_data', 'paid_at'
    ];

    /**
     * 生成訂單編號
     */
    public function generateOrderNo()
    {
        $prefix = 'PO' . date('Ymd');
        
        // 獲取當日最大編號
        $stmt = $this->db->prepare("
            SELECT order_no 
            FROM payment_orders 
            WHERE order_no LIKE ? 
            ORDER BY order_no DESC 
            LIMIT 1
        ");
        $stmt->execute([$prefix . '%']);
        $lastOrder = $stmt->fetch();
        
        if ($lastOrder) {
            $lastNo = intval(substr($lastOrder['order_no'], -6));
            $newNo = $lastNo + 1;
        } else {
            $newNo = 1;
        }
        
        return $prefix . str_pad($newNo, 6, '0', STR_PAD_LEFT);
    }

    /**
     * 創建訂單
     */
    public function createOrder($data)
    {
        // 驗證必要欄位
        if (!isset($data['tenant_id']) || !isset($data['plan_id']) || !isset($data['amount'])) {
            throw new Exception('缺少必要的訂單資訊');
        }
        
        // 生成訂單編號
        $data['order_no'] = $this->generateOrderNo();
        $data['status'] = 'pending';
        
        return $this->create($data);
    }

    /**
     * 根據訂單編號查找訂單
     */
    public function findByOrderNo($orderNo)
    {
        $stmt = $this->db->prepare("
            SELECT po.*, t.name as tenant_name, t.subdomain, sp.name as plan_name
            FROM payment_orders po
            LEFT JOIN tenants t ON po.tenant_id = t.id
            LEFT JOIN subscription_plans sp ON po.plan_id = sp.id
            WHERE po.order_no = ?
        ");
        $stmt->execute([$orderNo]);
        
        return $stmt->fetch();
    }

    /**
     * 更新訂單狀態
     */
    public function updateOrderStatus($orderNo, $status, $additionalData = [])
    {
        $data = array_merge(['status' => $status], $additionalData);
        
        // 如果是支付成功，設定支付時間
        if ($status === 'paid' && empty($data['paid_at'])) {
            $data['paid_at'] = date('Y-m-d H:i:s');
        }
        
        $stmt = $this->db->prepare("UPDATE payment_orders SET " . 
            implode(' = ?, ', array_keys($data)) . " = ? WHERE order_no = ?");
        
        $params = array_values($data);
        $params[] = $orderNo;
        
        return $stmt->execute($params);
    }

    /**
     * 處理支付成功
     */
    public function handlePaymentSuccess($orderNo, $transactionId, $paymentData = [])
    {
        $order = $this->findByOrderNo($orderNo);
        if (!$order) {
            throw new Exception('訂單不存在');
        }
        
        if ($order['status'] === 'paid') {
            throw new Exception('訂單已支付');
        }
        
        $this->beginTransaction();
        
        try {
            // 更新訂單狀態
            $this->updateOrderStatus($orderNo, 'paid', [
                'transaction_id' => $transactionId,
                'payment_data' => json_encode($paymentData, JSON_UNESCAPED_UNICODE)
            ]);
            
            // 創建或延長訂閱
            $subscriptionModel = new SubscriptionModel();
            $duration = $paymentData['duration_months'] ?? 1;
            $subscriptionModel->createSubscription(
                $order['tenant_id'],
                $order['plan_id'],
                $order['id'],
                $duration
            );
            
            // 更新租戶訂閱資訊
            $tenantModel = new TenantModel();
            $expiresAt = date('Y-m-d H:i:s', strtotime("+{$duration} months"));
            $tenantModel->updateSubscription($order['tenant_id'], $order['plan_id'], $expiresAt);
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 處理支付失敗
     */
    public function handlePaymentFailure($orderNo, $reason = '')
    {
        return $this->updateOrderStatus($orderNo, 'failed', [
            'payment_data' => json_encode(['failure_reason' => $reason], JSON_UNESCAPED_UNICODE)
        ]);
    }

    /**
     * 取消訂單
     */
    public function cancelOrder($orderNo, $reason = '')
    {
        return $this->updateOrderStatus($orderNo, 'cancelled', [
            'payment_data' => json_encode(['cancel_reason' => $reason], JSON_UNESCAPED_UNICODE)
        ]);
    }

    /**
     * 獲取租戶的訂單記錄
     */
    public function getTenantOrders($tenantId, $limit = null)
    {
        $sql = "
            SELECT po.*, sp.name as plan_name
            FROM payment_orders po
            LEFT JOIN subscription_plans sp ON po.plan_id = sp.id
            WHERE po.tenant_id = ?
            ORDER BY po.created_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取待處理的訂單
     */
    public function getPendingOrders($minutes = 30)
    {
        $stmt = $this->db->prepare("
            SELECT po.*, t.name as tenant_name, sp.name as plan_name
            FROM payment_orders po
            LEFT JOIN tenants t ON po.tenant_id = t.id
            LEFT JOIN subscription_plans sp ON po.plan_id = sp.id
            WHERE po.status = 'pending'
            AND po.created_at >= DATE_SUB(NOW(), INTERVAL ? MINUTE)
            ORDER BY po.created_at ASC
        ");
        $stmt->execute([$minutes]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取過期的待支付訂單
     */
    public function getExpiredOrders($minutes = 30)
    {
        $stmt = $this->db->prepare("
            SELECT po.*, t.name as tenant_name, sp.name as plan_name
            FROM payment_orders po
            LEFT JOIN tenants t ON po.tenant_id = t.id
            LEFT JOIN subscription_plans sp ON po.plan_id = sp.id
            WHERE po.status = 'pending'
            AND po.created_at < DATE_SUB(NOW(), INTERVAL ? MINUTE)
            ORDER BY po.created_at ASC
        ");
        $stmt->execute([$minutes]);
        
        return $stmt->fetchAll();
    }

    /**
     * 自動取消過期訂單
     */
    public function cancelExpiredOrders($minutes = 30)
    {
        $expiredOrders = $this->getExpiredOrders($minutes);
        $cancelledCount = 0;
        
        foreach ($expiredOrders as $order) {
            try {
                $this->cancelOrder($order['order_no'], '訂單超時自動取消');
                $cancelledCount++;
            } catch (Exception $e) {
                error_log('Failed to cancel expired order ' . $order['order_no'] . ': ' . $e->getMessage());
            }
        }
        
        return $cancelledCount;
    }

    /**
     * 獲取支付統計
     */
    public function getPaymentStats($period = 'month')
    {
        $stats = [];
        
        // 根據期間設定日期條件
        switch ($period) {
            case 'today':
                $dateCondition = "DATE(created_at) = CURDATE()";
                break;
            case 'week':
                $dateCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $dateCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            case 'year':
                $dateCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
                break;
            default:
                $dateCondition = "1=1";
        }
        
        // 按狀態統計
        $stmt = $this->db->prepare("
            SELECT status, COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount
            FROM payment_orders 
            WHERE {$dateCondition}
            GROUP BY status
        ");
        $stmt->execute();
        $statusStats = $stmt->fetchAll();
        $stats['by_status'] = [];
        foreach ($statusStats as $stat) {
            $stats['by_status'][$stat['status']] = [
                'count' => $stat['count'],
                'amount' => $stat['total_amount']
            ];
        }
        
        // 按支付方式統計
        $stmt = $this->db->prepare("
            SELECT payment_method, COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount
            FROM payment_orders 
            WHERE status = 'paid' AND {$dateCondition}
            GROUP BY payment_method
        ");
        $stmt->execute();
        $methodStats = $stmt->fetchAll();
        $stats['by_payment_method'] = [];
        foreach ($methodStats as $stat) {
            $stats['by_payment_method'][$stat['payment_method']] = [
                'count' => $stat['count'],
                'amount' => $stat['total_amount']
            ];
        }
        
        // 總收入
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_orders,
                COALESCE(SUM(amount), 0) as total_revenue,
                COALESCE(AVG(amount), 0) as avg_order_value
            FROM payment_orders 
            WHERE status = 'paid' AND {$dateCondition}
        ");
        $stmt->execute();
        $stats['revenue'] = $stmt->fetch();
        
        return $stats;
    }

    /**
     * 獲取收入趨勢
     */
    public function getRevenueTrend($days = 30)
    {
        $stmt = $this->db->prepare("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as order_count,
                COALESCE(SUM(amount), 0) as revenue
            FROM payment_orders
            WHERE status = 'paid'
            AND created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$days]);
        
        return $stmt->fetchAll();
    }

    /**
     * 退款處理
     */
    public function processRefund($orderNo, $refundAmount, $reason = '')
    {
        $order = $this->findByOrderNo($orderNo);
        if (!$order) {
            throw new Exception('訂單不存在');
        }
        
        if ($order['status'] !== 'paid') {
            throw new Exception('只能退款已支付的訂單');
        }
        
        if ($refundAmount > $order['amount']) {
            throw new Exception('退款金額不能超過訂單金額');
        }
        
        $this->beginTransaction();
        
        try {
            // 更新訂單狀態
            $refundData = [
                'refund_amount' => $refundAmount,
                'refund_reason' => $reason,
                'refund_at' => date('Y-m-d H:i:s')
            ];
            
            $status = $refundAmount >= $order['amount'] ? 'refunded' : 'partial_refunded';
            $this->updateOrderStatus($orderNo, $status, [
                'payment_data' => json_encode($refundData, JSON_UNESCAPED_UNICODE)
            ]);
            
            // 如果是全額退款，取消相關訂閱
            if ($refundAmount >= $order['amount']) {
                $subscriptionModel = new SubscriptionModel();
                $subscriptionModel->cancelSubscription($order['tenant_id']);
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
}

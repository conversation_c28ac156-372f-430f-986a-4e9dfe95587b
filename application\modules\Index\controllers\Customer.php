<?php
/**
 * 客戶管理控制器
 */
class CustomerController extends BaseController
{
    /**
     * 客戶列表
     */
    public function indexAction()
    {
        if (!$this->hasPermission('customer.view')) {
            return $this->error('權限不足', 403);
        }
        
        $page = $this->getParam('page', 1);
        $search = $this->getParam('search', '');
        $perPage = 15;
        
        $customerModel = new CustomerModel();
        $query = $customerModel->query();
        
        // 搜索條件
        if (!empty($search)) {
            $query->where("(name LIKE ? OR phone LIKE ? OR mobile LIKE ? OR customer_no LIKE ?)", 
                         "%{$search}%", "%{$search}%", "%{$search}%", "%{$search}%");
        }
        
        $query->orderBy('created_at', 'DESC');
        
        $result = $this->paginate($query, $page, $perPage);
        
        // 獲取統計數據
        $stats = $customerModel->getStats();
        
        $this->getView()->assign('customers', $result['data']);
        $this->getView()->assign('pagination', $result['pagination']);
        $this->getView()->assign('search', $search);
        $this->getView()->assign('stats', $stats);
        $this->getView()->assign('title', '客戶管理');
    }

    /**
     * 客戶詳情
     */
    public function viewAction()
    {
        if (!$this->hasPermission('customer.view')) {
            return $this->error('權限不足', 403);
        }
        
        $customerId = $this->getParam('id');
        if (!$customerId) {
            return $this->error('客戶ID不能為空', 400);
        }
        
        $customerModel = new CustomerModel();
        $customer = $customerModel->getCustomerDetails($customerId);
        
        if (!$customer) {
            return $this->error('客戶不存在', 404);
        }
        
        // 獲取客戶消費記錄
        $spendingHistory = $customerModel->getCustomerSpending($customerId, 12);
        
        $this->getView()->assign('customer', $customer);
        $this->getView()->assign('spendingHistory', $spendingHistory);
        $this->getView()->assign('title', '客戶詳情 - ' . $customer['name']);
    }

    /**
     * 新增客戶
     */
    public function addAction()
    {
        if (!$this->hasPermission('customer.create')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleAddCustomer();
        }
        
        $this->getView()->assign('title', '新增客戶');
    }

    /**
     * 處理新增客戶
     */
    private function handleAddCustomer()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'name' => 'required|max:100',
            'phone' => 'phone',
            'mobile' => 'mobile',
            'email' => 'email|max:100',
            'id_number' => 'id_number',
            'address' => 'max:255',
            'birthday' => 'date',
            'gender' => 'in:male,female',
            'notes' => 'max:500'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        // 檢查必要聯絡方式
        if (empty($data['phone']) && empty($data['mobile'])) {
            if ($this->isAjax()) {
                return $this->error('請至少填寫一個聯絡電話');
            }
            $this->getView()->assign('error', '請至少填寫一個聯絡電話');
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        try {
            $customerModel = new CustomerModel();
            
            $customerData = [
                'name' => $data['name'],
                'phone' => $data['phone'] ?? null,
                'mobile' => $data['mobile'] ?? null,
                'email' => $data['email'] ?? null,
                'id_number' => $data['id_number'] ?? null,
                'address' => $data['address'] ?? null,
                'birthday' => $data['birthday'] ?? null,
                'gender' => $data['gender'] ?? null,
                'notes' => $data['notes'] ?? null
            ];
            
            $customer = $customerModel->create($customerData);
            
            // 記錄操作日誌
            $this->logActivity('customer_create', 'customers', $customer['id'], null, $customerData);
            
            if ($this->isAjax()) {
                return $this->success(['customer_id' => $customer['id']], '客戶新增成功');
            }
            
            return $this->redirect('/customer/view/' . $customer['id'] . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('新增失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '新增失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 編輯客戶
     */
    public function editAction()
    {
        if (!$this->hasPermission('customer.edit')) {
            return $this->error('權限不足', 403);
        }
        
        $customerId = $this->getParam('id');
        if (!$customerId) {
            return $this->error('客戶ID不能為空', 400);
        }
        
        $customerModel = new CustomerModel();
        $customer = $customerModel->find($customerId);
        
        if (!$customer) {
            return $this->error('客戶不存在', 404);
        }
        
        if ($this->isPost()) {
            return $this->handleEditCustomer($customerId);
        }
        
        $this->getView()->assign('customer', $customer);
        $this->getView()->assign('title', '編輯客戶 - ' . $customer['name']);
    }

    /**
     * 處理編輯客戶
     */
    private function handleEditCustomer($customerId)
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'name' => 'required|max:100',
            'phone' => 'phone',
            'mobile' => 'mobile',
            'email' => 'email|max:100',
            'id_number' => 'id_number',
            'address' => 'max:255',
            'birthday' => 'date',
            'gender' => 'in:male,female',
            'notes' => 'max:500'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        // 檢查必要聯絡方式
        if (empty($data['phone']) && empty($data['mobile'])) {
            if ($this->isAjax()) {
                return $this->error('請至少填寫一個聯絡電話');
            }
            $this->getView()->assign('error', '請至少填寫一個聯絡電話');
            return;
        }
        
        try {
            $customerModel = new CustomerModel();
            $oldData = $customerModel->find($customerId);
            
            $updateData = [
                'name' => $data['name'],
                'phone' => $data['phone'] ?? null,
                'mobile' => $data['mobile'] ?? null,
                'email' => $data['email'] ?? null,
                'id_number' => $data['id_number'] ?? null,
                'address' => $data['address'] ?? null,
                'birthday' => $data['birthday'] ?? null,
                'gender' => $data['gender'] ?? null,
                'notes' => $data['notes'] ?? null
            ];
            
            $customerModel->update($customerId, $updateData);
            
            // 記錄操作日誌
            $this->logActivity('customer_update', 'customers', $customerId, $oldData, $updateData);
            
            if ($this->isAjax()) {
                return $this->success(null, '客戶更新成功');
            }
            
            return $this->redirect('/customer/view/' . $customerId . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 刪除客戶
     */
    public function deleteAction()
    {
        if (!$this->hasPermission('customer.delete')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $customerId = $this->getPost('customer_id');
        if (!$customerId) {
            return $this->error('客戶ID不能為空', 400);
        }
        
        try {
            $customerModel = new CustomerModel();
            $customer = $customerModel->find($customerId);
            
            if (!$customer) {
                return $this->error('客戶不存在', 404);
            }
            
            // 檢查是否可以刪除
            if (!$customerModel->canDelete($customerId)) {
                return $this->error('該客戶有關聯的車輛或工單，無法刪除');
            }
            
            // 軟刪除
            $customerModel->softDelete($customerId);
            
            // 記錄操作日誌
            $this->logActivity('customer_delete', 'customers', $customerId, $customer);
            
            return $this->success(null, '客戶刪除成功');
            
        } catch (Exception $e) {
            return $this->error('刪除失敗：' . $e->getMessage());
        }
    }

    /**
     * 搜索客戶（AJAX）
     */
    public function searchAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        if (!$this->hasPermission('customer.view')) {
            return $this->error('權限不足', 403);
        }
        
        $query = $this->getParam('q', '');
        $limit = $this->getParam('limit', 10);
        
        if (strlen($query) < 2) {
            return $this->success([]);
        }
        
        $customerModel = new CustomerModel();
        $customers = $customerModel->search($query, $limit);
        
        return $this->success($customers);
    }

    /**
     * 匯出客戶資料
     */
    public function exportAction()
    {
        if (!$this->hasPermission('customer.view')) {
            return $this->error('權限不足', 403);
        }
        
        $format = $this->getParam('format', 'csv');
        
        $customerModel = new CustomerModel();
        $customers = $customerModel->findAll(['is_active' => 1], 'created_at DESC');
        
        switch ($format) {
            case 'csv':
                return $this->exportToCsv($customers);
            case 'excel':
                return $this->exportToExcel($customers);
            default:
                return $this->error('不支援的匯出格式');
        }
    }

    /**
     * 匯出為 CSV
     */
    private function exportToCsv($customers)
    {
        $filename = '客戶資料_' . date('Y-m-d') . '.csv';
        
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // 輸出 BOM 以支援中文
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // 標題行
        fputcsv($output, [
            '客戶編號', '姓名', '電話', '手機', '電子郵件', 
            '身分證字號', '地址', '生日', '性別', '備註', '建立時間'
        ]);
        
        // 數據行
        foreach ($customers as $customer) {
            fputcsv($output, [
                $customer['customer_no'],
                $customer['name'],
                $customer['phone'],
                $customer['mobile'],
                $customer['email'],
                $customer['id_number'],
                $customer['address'],
                $customer['birthday'],
                $customer['gender'] === 'male' ? '男' : ($customer['gender'] === 'female' ? '女' : ''),
                $customer['notes'],
                $customer['created_at']
            ]);
        }
        
        fclose($output);
        return false; // 阻止視圖渲染
    }

    /**
     * 匯出為 Excel（簡化版）
     */
    private function exportToExcel($customers)
    {
        // 這裡可以使用 PhpSpreadsheet 等庫
        // 暫時使用 CSV 格式
        return $this->exportToCsv($customers);
    }
}

<?php
/**
 * 零件模型
 */
class PartModel extends BaseModel
{
    protected $table = 'parts';
    
    protected $fillable = [
        'category_id', 'part_no', 'name', 'brand', 'specification', 'unit',
        'cost_price', 'selling_price', 'stock_quantity', 'min_stock', 'max_stock',
        'location', 'supplier', 'notes', 'is_active'
    ];

    /**
     * 搜索零件
     */
    public function search($query, $limit = 10)
    {
        $sql = "
            SELECT p.*, pc.name as category_name
            FROM parts p
            LEFT JOIN part_categories pc ON p.category_id = pc.id
            WHERE p.tenant_id = ?
            AND p.is_active = 1
            AND (
                p.part_no LIKE ? OR
                p.name LIKE ? OR
                p.brand LIKE ? OR
                p.specification LIKE ?
            )
            ORDER BY p.name ASC
            LIMIT ?
        ";
        
        $searchTerm = "%{$query}%";
        $tenantId = $this->getCurrentTenantId();
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取低庫存零件
     */
    public function getLowStockParts($limit = null)
    {
        $sql = "
            SELECT p.*, pc.name as category_name
            FROM parts p
            LEFT JOIN part_categories pc ON p.category_id = pc.id
            WHERE p.tenant_id = ?
            AND p.is_active = 1
            AND p.stock_quantity <= p.min_stock
            ORDER BY (p.stock_quantity / NULLIF(p.min_stock, 0)) ASC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->getCurrentTenantId()]);
        
        return $stmt->fetchAll();
    }

    /**
     * 檢查零件編號是否可用
     */
    public function isPartNoAvailable($partNo, $excludeId = null)
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return false;
        }
        
        $sql = "SELECT COUNT(*) as count FROM parts WHERE tenant_id = ? AND part_no = ?";
        $params = [$tenantId, $partNo];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] == 0;
    }

    /**
     * 更新庫存
     */
    public function updateStock($partId, $quantity, $operation = 'add')
    {
        $part = $this->find($partId);
        if (!$part) {
            return false;
        }
        
        $newQuantity = $part['stock_quantity'];
        
        switch ($operation) {
            case 'add':
                $newQuantity += $quantity;
                break;
            case 'subtract':
                $newQuantity -= $quantity;
                break;
            case 'set':
                $newQuantity = $quantity;
                break;
        }
        
        // 確保庫存不為負數
        if ($newQuantity < 0) {
            throw new Exception('庫存不足');
        }
        
        return $this->update($partId, ['stock_quantity' => $newQuantity]);
    }

    /**
     * 批量更新庫存
     */
    public function batchUpdateStock($updates)
    {
        $this->beginTransaction();
        
        try {
            foreach ($updates as $update) {
                $this->updateStock($update['part_id'], $update['quantity'], $update['operation']);
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 獲取零件統計
     */
    public function getStats()
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stats = [];
        
        // 總零件數
        $stats['total'] = $this->count(['is_active' => 1]);
        
        // 低庫存零件數
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM parts 
            WHERE tenant_id = ? 
            AND is_active = 1 
            AND stock_quantity <= min_stock
        ");
        $stmt->execute([$tenantId]);
        $stats['low_stock'] = $stmt->fetch()['count'];
        
        // 零庫存零件數
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM parts 
            WHERE tenant_id = ? 
            AND is_active = 1 
            AND stock_quantity = 0
        ");
        $stmt->execute([$tenantId]);
        $stats['out_of_stock'] = $stmt->fetch()['count'];
        
        // 按分類統計
        $stmt = $this->db->prepare("
            SELECT pc.name as category_name, COUNT(p.id) as count 
            FROM parts p
            LEFT JOIN part_categories pc ON p.category_id = pc.id
            WHERE p.tenant_id = ? AND p.is_active = 1
            GROUP BY p.category_id, pc.name
            ORDER BY count DESC
        ");
        $stmt->execute([$tenantId]);
        $stats['by_category'] = $stmt->fetchAll();
        
        // 庫存價值
        $stmt = $this->db->prepare("
            SELECT 
                SUM(stock_quantity * cost_price) as cost_value,
                SUM(stock_quantity * selling_price) as selling_value
            FROM parts 
            WHERE tenant_id = ? AND is_active = 1
        ");
        $stmt->execute([$tenantId]);
        $valueStats = $stmt->fetch();
        $stats['inventory_value'] = $valueStats;
        
        return $stats;
    }

    /**
     * 獲取零件使用記錄
     */
    public function getUsageHistory($partId, $months = 12)
    {
        $stmt = $this->db->prepare("
            SELECT 
                DATE_FORMAT(wo.created_at, '%Y-%m') as month,
                SUM(wop.quantity) as total_used,
                COUNT(DISTINCT wo.id) as order_count
            FROM work_order_parts wop
            JOIN work_orders wo ON wop.work_order_id = wo.id
            WHERE wop.part_id = ?
            AND wo.created_at >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            GROUP BY DATE_FORMAT(wo.created_at, '%Y-%m')
            ORDER BY month DESC
        ");
        $stmt->execute([$partId, $months]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取熱門零件
     */
    public function getPopularParts($limit = 10, $months = 3)
    {
        $stmt = $this->db->prepare("
            SELECT p.*, pc.name as category_name, SUM(wop.quantity) as total_used
            FROM parts p
            LEFT JOIN part_categories pc ON p.category_id = pc.id
            JOIN work_order_parts wop ON p.id = wop.part_id
            JOIN work_orders wo ON wop.work_order_id = wo.id
            WHERE p.tenant_id = ?
            AND wo.created_at >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            GROUP BY p.id
            ORDER BY total_used DESC
            LIMIT ?
        ");
        $stmt->execute([$this->getCurrentTenantId(), $months, $limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * 檢查零件是否可以刪除
     */
    public function canDelete($partId)
    {
        // 檢查是否有關聯的工單零件記錄
        $usageCount = $this->db->prepare("SELECT COUNT(*) as count FROM work_order_parts WHERE part_id = ?");
        $usageCount->execute([$partId]);
        if ($usageCount->fetch()['count'] > 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 生成零件條碼
     */
    public function generateBarcode($partId)
    {
        $part = $this->find($partId);
        if (!$part) {
            return null;
        }
        
        // 使用零件ID和租戶ID生成唯一條碼
        $tenantId = $this->getCurrentTenantId();
        return sprintf('%04d%06d', $tenantId, $partId);
    }

    /**
     * 根據條碼查找零件
     */
    public function findByBarcode($barcode)
    {
        if (strlen($barcode) !== 10) {
            return null;
        }
        
        $tenantId = intval(substr($barcode, 0, 4));
        $partId = intval(substr($barcode, 4));
        
        if ($tenantId !== $this->getCurrentTenantId()) {
            return null;
        }
        
        return $this->find($partId);
    }
}

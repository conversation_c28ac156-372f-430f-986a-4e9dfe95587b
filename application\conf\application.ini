[product]
; 應用程式基本配置
application.directory = APPLICATION_PATH
application.modules = "Index,Tenant,Admin,Payment,Print"
application.library = APPLICATION_PATH "/library"
application.bootstrap = APPLICATION_PATH "/Bootstrap.php"
application.view.ext = "phtml"

; 資料庫配置
database.host = "localhost"
database.port = 3306
database.username = "root"
database.password = ""
database.dbname = "cars_manage"
database.charset = "utf8mb4"

; Redis 配置 (用於 Session 和快取)
redis.host = "127.0.0.1"
redis.port = 6379
redis.database = 0

; 系統配置
system.timezone = "Asia/Taipei"
system.default_language = "zh-TW"
system.session_lifetime = 7200
system.upload_max_size = "10M"

; 安全配置
security.password_salt = "CarManage2024!@#"
security.jwt_secret = "CarManageJWT2024Secret"
security.csrf_token_name = "_token"

; 支付配置
payment.ecpay.merchant_id = ""
payment.ecpay.hash_key = ""
payment.ecpay.hash_iv = ""
payment.ecpay.test_mode = true

; 郵件配置
mail.smtp_host = ""
mail.smtp_port = 587
mail.smtp_username = ""
mail.smtp_password = ""
mail.from_email = "<EMAIL>"
mail.from_name = "汽車保養廠管理系統"

[development : product]
; 開發環境配置
application.showErrors = 1
database.username = "root"
database.password = ""
payment.ecpay.test_mode = true
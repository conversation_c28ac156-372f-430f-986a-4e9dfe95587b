<?php
/**
 * 認證控制器
 */
class AuthController extends BaseController
{
    /**
     * 登入頁面
     */
    public function loginAction()
    {
        // 如果已登入，重定向到儀表板
        if ($this->user) {
            return $this->redirect('/dashboard');
        }
        
        if ($this->isPost()) {
            return $this->handleLogin();
        }
        
        $this->getView()->assign('title', '用戶登入');
    }

    /**
     * 處理登入
     */
    private function handleLogin()
    {
        $login = $this->getPost('login');
        $password = $this->getPost('password');
        $remember = $this->getPost('remember');
        
        // 驗證輸入
        $validation = $this->validate([
            'login' => $login,
            'password' => $password
        ], [
            'login' => 'required',
            'password' => 'required'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請填寫完整的登入資訊');
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        // 檢查租戶
        if (!$this->tenant) {
            if ($this->isAjax()) {
                return $this->error('無效的租戶');
            }
            $this->getView()->assign('error', '無效的租戶');
            return;
        }
        
        // 驗證用戶
        $userModel = new UserModel();
        $user = $userModel->authenticate($login, $password);
        
        if (!$user) {
            if ($this->isAjax()) {
                return $this->error('用戶名或密碼錯誤');
            }
            $this->getView()->assign('error', '用戶名或密碼錯誤');
            return;
        }
        
        // 設定 session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['tenant_id'] = $user['tenant_id'];
        
        // 記住我功能
        if ($remember) {
            $token = bin2hex(random_bytes(32));
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30天
            // 這裡應該將 token 存儲到資料庫中
        }
        
        // 記錄登入日誌
        $this->logActivity('user_login', 'users', $user['id']);
        
        if ($this->isAjax()) {
            return $this->success(['redirect' => '/dashboard'], '登入成功');
        }
        
        return $this->redirect('/dashboard');
    }

    /**
     * 註冊頁面
     */
    public function registerAction()
    {
        // 如果已登入，重定向到儀表板
        if ($this->user) {
            return $this->redirect('/dashboard');
        }
        
        if ($this->isPost()) {
            return $this->handleRegister();
        }
        
        // 獲取訂閱方案
        $planModel = new SubscriptionPlanModel();
        $plans = $planModel->getActivePlans();
        
        $this->getView()->assign('plans', $plans);
        $this->getView()->assign('title', '租戶註冊');
    }

    /**
     * 處理註冊
     */
    private function handleRegister()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'tenant_name' => 'required|max:100',
            'subdomain' => 'required|min:3|max:50',
            'admin_name' => 'required|max:100',
            'admin_email' => 'required|email|max:100',
            'admin_username' => 'required|min:3|max:50',
            'admin_password' => 'required|min:6',
            'admin_password_confirmation' => 'required|confirmed',
            'plan_id' => 'required|integer',
            'agree_terms' => 'required'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        // 檢查子域名是否可用
        $tenantModel = new TenantModel();
        if (!$tenantModel->isSubdomainAvailable($data['subdomain'])) {
            if ($this->isAjax()) {
                return $this->error('子域名已被使用');
            }
            $this->getView()->assign('error', '子域名已被使用');
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        try {
            // 創建租戶和管理員
            $tenantData = [
                'name' => $data['tenant_name'],
                'subdomain' => $data['subdomain'],
                'current_plan_id' => $data['plan_id'],
                'status' => 'trial',
                'trial_expires_at' => date('Y-m-d H:i:s', strtotime('+14 days'))
            ];
            
            $adminData = [
                'full_name' => $data['admin_name'],
                'email' => $data['admin_email'],
                'username' => $data['admin_username'],
                'password' => $data['admin_password']
            ];
            
            $tenant = $tenantModel->createWithAdmin($tenantData, $adminData);
            
            if ($this->isAjax()) {
                return $this->success([
                    'tenant_id' => $tenant['id'],
                    'subdomain' => $tenant['subdomain'],
                    'redirect' => '/tenant/' . $tenant['subdomain']
                ], '註冊成功！請使用您的帳號登入。');
            }
            
            return $this->redirect('/tenant/' . $tenant['subdomain'] . '?registered=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('註冊失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '註冊失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 登出
     */
    public function logoutAction()
    {
        if ($this->user) {
            $this->logActivity('user_logout', 'users', $this->user['id']);
        }
        
        // 清除 session
        session_destroy();
        
        // 清除記住我 cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
        
        if ($this->isAjax()) {
            return $this->success(['redirect' => '/'], '已成功登出');
        }
        
        return $this->redirect('/');
    }

    /**
     * 忘記密碼
     */
    public function forgotPasswordAction()
    {
        if ($this->isPost()) {
            return $this->handleForgotPassword();
        }
        
        $this->getView()->assign('title', '忘記密碼');
    }

    /**
     * 處理忘記密碼
     */
    private function handleForgotPassword()
    {
        $email = $this->getPost('email');
        
        $validation = $this->validate(['email' => $email], ['email' => 'required|email']);
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請輸入有效的電子郵件地址');
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        // 查找用戶
        $userModel = new UserModel();
        $user = $userModel->findByLogin($email);
        
        if ($user) {
            // 生成重設密碼 token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // 存儲 token（這裡應該有一個 password_resets 表）
            $this->storePasswordResetToken($user['id'], $token, $expires);
            
            // 發送重設密碼郵件
            $this->sendPasswordResetEmail($user, $token);
        }
        
        // 無論是否找到用戶，都顯示相同訊息（安全考量）
        if ($this->isAjax()) {
            return $this->success(null, '如果該郵箱存在，重設密碼連結已發送到您的郵箱');
        }
        
        $this->getView()->assign('success', '如果該郵箱存在，重設密碼連結已發送到您的郵箱');
    }

    /**
     * 重設密碼
     */
    public function resetPasswordAction()
    {
        $token = $this->getParam('token');
        
        if (!$token) {
            return $this->redirect('/auth/forgot-password');
        }
        
        // 驗證 token
        $resetData = $this->getPasswordResetData($token);
        if (!$resetData || strtotime($resetData['expires_at']) < time()) {
            $this->getView()->assign('error', '重設密碼連結已過期或無效');
            return;
        }
        
        if ($this->isPost()) {
            return $this->handleResetPassword($resetData);
        }
        
        $this->getView()->assign('token', $token);
        $this->getView()->assign('title', '重設密碼');
    }

    /**
     * 處理重設密碼
     */
    private function handleResetPassword($resetData)
    {
        $password = $this->getPost('password');
        $passwordConfirmation = $this->getPost('password_confirmation');
        
        $validation = $this->validate([
            'password' => $password,
            'password_confirmation' => $passwordConfirmation
        ], [
            'password' => 'required|min:6',
            'password_confirmation' => 'required|confirmed'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查密碼格式', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        try {
            // 更新密碼
            $userModel = new UserModel();
            $userModel->resetPassword($resetData['user_id'], $password);
            
            // 刪除重設 token
            $this->deletePasswordResetToken($resetData['token']);
            
            if ($this->isAjax()) {
                return $this->success(['redirect' => '/auth/login'], '密碼重設成功，請重新登入');
            }
            
            return $this->redirect('/auth/login?reset=success');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('密碼重設失敗');
            }
            $this->getView()->assign('error', '密碼重設失敗');
        }
    }

    /**
     * 存儲密碼重設 token
     */
    private function storePasswordResetToken($userId, $token, $expires)
    {
        // 這裡應該實現密碼重設 token 的存儲邏輯
        // 可以存儲在資料庫或 Redis 中
    }

    /**
     * 獲取密碼重設數據
     */
    private function getPasswordResetData($token)
    {
        // 這裡應該實現從存儲中獲取密碼重設數據的邏輯
        return null;
    }

    /**
     * 刪除密碼重設 token
     */
    private function deletePasswordResetToken($token)
    {
        // 這裡應該實現刪除密碼重設 token 的邏輯
    }

    /**
     * 發送密碼重設郵件
     */
    private function sendPasswordResetEmail($user, $token)
    {
        // 這裡應該實現發送郵件的邏輯
    }
}

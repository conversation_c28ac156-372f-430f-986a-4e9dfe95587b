<?php
/**
 * 支付控制器
 */
class IndexController extends BaseController
{
    /**
     * 支付首頁
     */
    public function indexAction()
    {
        return $this->redirect('/payment/checkout');
    }

    /**
     * 支付結帳頁面
     */
    public function checkoutAction()
    {
        $orderNo = $this->getParam('order_no');
        if (!$orderNo) {
            return $this->error('訂單編號不能為空', 400);
        }
        
        $paymentOrderModel = new PaymentOrderModel();
        $order = $paymentOrderModel->findByOrderNo($orderNo);
        
        if (!$order) {
            return $this->error('訂單不存在', 404);
        }
        
        if ($order['status'] !== 'pending') {
            return $this->error('訂單狀態異常', 400);
        }
        
        // 檢查訂單是否過期（30分鐘）
        $createdAt = new DateTime($order['created_at']);
        $now = new DateTime();
        $diff = $now->diff($createdAt);
        $minutes = ($diff->h * 60) + $diff->i;
        
        if ($minutes > 30) {
            $paymentOrderModel->cancelOrder($orderNo, '訂單超時');
            return $this->error('訂單已過期', 400);
        }
        
        // 生成支付表單
        $ecpayService = new EcpayService();
        $paymentForm = $ecpayService->generatePaymentForm($order);
        
        $this->getView()->assign('order', $order);
        $this->getView()->assign('paymentForm', $paymentForm);
        $this->getView()->assign('title', '支付結帳');
    }

    /**
     * 綠界支付回調
     */
    public function ecpayNotifyAction()
    {
        // 停用視圖渲染
        Yaf_Dispatcher::getInstance()->disableView();
        
        try {
            $ecpayService = new EcpayService();
            $result = $ecpayService->handleNotify($_POST);
            
            if ($result['success']) {
                echo '1|OK';
            } else {
                echo '0|' . $result['message'];
            }
            
        } catch (Exception $e) {
            error_log('ECPay notify error: ' . $e->getMessage());
            echo '0|Error';
        }
    }

    /**
     * 支付返回頁面
     */
    public function returnAction()
    {
        $orderNo = $this->getParam('order_no');
        $status = $this->getParam('status', 'pending');
        
        if (!$orderNo) {
            return $this->redirect('/');
        }
        
        $paymentOrderModel = new PaymentOrderModel();
        $order = $paymentOrderModel->findByOrderNo($orderNo);
        
        if (!$order) {
            return $this->error('訂單不存在', 404);
        }
        
        $this->getView()->assign('order', $order);
        $this->getView()->assign('status', $status);
        $this->getView()->assign('title', '支付結果');
    }

    /**
     * 訂閱支付頁面
     */
    public function subscribeAction()
    {
        $reason = $this->getParam('reason');
        $planId = $this->getParam('plan_id');
        
        // 如果沒有租戶，重定向到註冊
        if (!$this->tenant) {
            return $this->redirect('/tenant/register');
        }
        
        if ($this->isPost()) {
            return $this->handleSubscribePayment();
        }
        
        // 獲取可用方案
        $planModel = new SubscriptionPlanModel();
        $plans = $planModel->getActivePlans();
        
        // 獲取當前訂閱資訊
        $subscriptionModel = new SubscriptionModel();
        $currentSubscription = $subscriptionModel->getCurrentSubscription($this->tenant['id']);
        
        $this->getView()->assign('plans', $plans);
        $this->getView()->assign('currentSubscription', $currentSubscription);
        $this->getView()->assign('reason', $reason);
        $this->getView()->assign('selectedPlanId', $planId);
        $this->getView()->assign('title', '訂閱支付');
    }

    /**
     * 處理訂閱支付
     */
    private function handleSubscribePayment()
    {
        $planId = $this->getPost('plan_id');
        $duration = $this->getPost('duration', 1);
        
        // 驗證輸入
        $validation = $this->validate([
            'plan_id' => $planId,
            'duration' => $duration
        ], [
            'plan_id' => 'required|integer',
            'duration' => 'required|integer|min:1|max:12'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        // 檢查方案是否有效
        $planModel = new SubscriptionPlanModel();
        $plan = $planModel->find($planId);
        if (!$plan || !$plan['is_active']) {
            if ($this->isAjax()) {
                return $this->error('選擇的方案無效');
            }
            $this->getView()->assign('error', '選擇的方案無效');
            return;
        }
        
        try {
            // 創建支付訂單
            $paymentOrderModel = new PaymentOrderModel();
            $amount = $plan['price'] * $duration;
            
            $orderData = [
                'tenant_id' => $this->tenant['id'],
                'plan_id' => $planId,
                'amount' => $amount,
                'duration_months' => $duration
            ];
            
            $order = $paymentOrderModel->createOrder($orderData);
            
            if ($this->isAjax()) {
                return $this->success([
                    'order_id' => $order['id'],
                    'order_no' => $order['order_no'],
                    'redirect' => '/payment/checkout/' . $order['order_no']
                ], '訂單創建成功');
            }
            
            return $this->redirect('/payment/checkout/' . $order['order_no']);
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('創建訂單失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '創建訂單失敗：' . $e->getMessage());
        }
    }

    /**
     * 查詢訂單狀態
     */
    public function statusAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        $orderNo = $this->getParam('order_no');
        if (!$orderNo) {
            return $this->error('訂單編號不能為空', 400);
        }
        
        $paymentOrderModel = new PaymentOrderModel();
        $order = $paymentOrderModel->findByOrderNo($orderNo);
        
        if (!$order) {
            return $this->error('訂單不存在', 404);
        }
        
        return $this->success([
            'order_no' => $order['order_no'],
            'status' => $order['status'],
            'amount' => $order['amount'],
            'paid_at' => $order['paid_at'],
            'created_at' => $order['created_at']
        ]);
    }

    /**
     * 取消訂單
     */
    public function cancelAction()
    {
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $orderNo = $this->getPost('order_no');
        $reason = $this->getPost('reason', '用戶取消');
        
        if (!$orderNo) {
            return $this->error('訂單編號不能為空', 400);
        }
        
        try {
            $paymentOrderModel = new PaymentOrderModel();
            $order = $paymentOrderModel->findByOrderNo($orderNo);
            
            if (!$order) {
                return $this->error('訂單不存在', 404);
            }
            
            if ($order['status'] !== 'pending') {
                return $this->error('只能取消待支付的訂單');
            }
            
            $paymentOrderModel->cancelOrder($orderNo, $reason);
            
            return $this->success(null, '訂單已取消');
            
        } catch (Exception $e) {
            return $this->error('取消訂單失敗：' . $e->getMessage());
        }
    }

    /**
     * 支付記錄
     */
    public function historyAction()
    {
        if (!$this->tenant || !$this->user) {
            return $this->redirect('/auth/login');
        }
        
        if (!$this->hasPermission('payment.view')) {
            return $this->error('權限不足', 403);
        }
        
        $page = $this->getParam('page', 1);
        $perPage = 15;
        
        $paymentOrderModel = new PaymentOrderModel();
        $query = $paymentOrderModel->query()
            ->where('tenant_id', $this->tenant['id'])
            ->orderBy('created_at', 'DESC');
        
        $result = $this->paginate($query, $page, $perPage);
        
        $this->getView()->assign('orders', $result['data']);
        $this->getView()->assign('pagination', $result['pagination']);
        $this->getView()->assign('title', '支付記錄');
    }

    /**
     * 申請退款
     */
    public function refundAction()
    {
        if (!$this->tenant || !$this->user) {
            return $this->error('請先登入', 401);
        }
        
        if (!$this->hasPermission('payment.refund')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $orderNo = $this->getPost('order_no');
        $refundAmount = $this->getPost('refund_amount');
        $reason = $this->getPost('reason', '');
        
        // 驗證輸入
        $validation = $this->validate([
            'order_no' => $orderNo,
            'refund_amount' => $refundAmount,
            'reason' => $reason
        ], [
            'order_no' => 'required',
            'refund_amount' => 'required|numeric|min:0.01',
            'reason' => 'required|max:255'
        ]);
        
        if (!$validation['valid']) {
            return $this->error('請檢查輸入資料', 400, $validation['errors']);
        }
        
        try {
            $paymentOrderModel = new PaymentOrderModel();
            $order = $paymentOrderModel->findByOrderNo($orderNo);
            
            if (!$order) {
                return $this->error('訂單不存在', 404);
            }
            
            if ($order['tenant_id'] != $this->tenant['id']) {
                return $this->error('權限不足', 403);
            }
            
            // 處理退款
            $paymentOrderModel->processRefund($orderNo, $refundAmount, $reason);
            
            // 記錄操作日誌
            $this->logActivity('payment_refund', 'payment_orders', $order['id'], null, [
                'refund_amount' => $refundAmount,
                'reason' => $reason
            ]);
            
            return $this->success(null, '退款申請已提交');
            
        } catch (Exception $e) {
            return $this->error('退款申請失敗：' . $e->getMessage());
        }
    }
}

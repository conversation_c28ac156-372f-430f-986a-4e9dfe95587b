<?php
/**
 * 零件分類模型
 */
class PartCategoryModel extends BaseModel
{
    protected $table = 'part_categories';
    
    protected $fillable = [
        'name', 'description', 'parent_id', 'sort_order', 'is_active'
    ];

    /**
     * 獲取所有分類（樹狀結構）
     */
    public function getCategoryTree()
    {
        $categories = $this->findAll(['is_active' => 1], 'sort_order ASC, name ASC');
        return $this->buildTree($categories);
    }

    /**
     * 建立樹狀結構
     */
    private function buildTree($categories, $parentId = null)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['children'] = $this->buildTree($categories, $category['id']);
                $tree[] = $category;
            }
        }
        
        return $tree;
    }

    /**
     * 獲取分類路徑
     */
    public function getCategoryPath($categoryId)
    {
        $path = [];
        $category = $this->find($categoryId);
        
        while ($category) {
            array_unshift($path, $category);
            $category = $category['parent_id'] ? $this->find($category['parent_id']) : null;
        }
        
        return $path;
    }

    /**
     * 檢查是否可以刪除
     */
    public function canDelete($categoryId)
    {
        // 檢查是否有子分類
        $childCount = $this->count(['parent_id' => $categoryId]);
        if ($childCount > 0) {
            return false;
        }
        
        // 檢查是否有關聯的零件
        $partCount = $this->db->prepare("SELECT COUNT(*) as count FROM parts WHERE category_id = ?");
        $partCount->execute([$categoryId]);
        if ($partCount->fetch()['count'] > 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 獲取分類統計
     */
    public function getCategoryStats()
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stmt = $this->db->prepare("
            SELECT pc.id, pc.name, COUNT(p.id) as part_count
            FROM part_categories pc
            LEFT JOIN parts p ON pc.id = p.category_id AND p.tenant_id = ? AND p.is_active = 1
            WHERE pc.tenant_id = ? AND pc.is_active = 1
            GROUP BY pc.id, pc.name
            ORDER BY pc.sort_order ASC, pc.name ASC
        ");
        $stmt->execute([$tenantId, $tenantId]);
        
        return $stmt->fetchAll();
    }
}

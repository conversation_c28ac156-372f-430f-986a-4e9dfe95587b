<?php
/**
 * 汽車保養廠管理系統入口文件
 */

// 定義應用程式路徑
defined('APPLICATION_PATH') || define('APPLICATION_PATH', __DIR__ . '/../application');

// 定義環境
defined('APPLICATION_ENV') || define('APPLICATION_ENV', getenv('APPLICATION_ENV') ?: 'development');

// 錯誤報告設定
if (APPLICATION_ENV === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 設定時區
date_default_timezone_set('Asia/Taipei');

// 自動載入類別
spl_autoload_register(function ($className) {
    $paths = [
        APPLICATION_PATH . '/library/' . $className . '.php',
        APPLICATION_PATH . '/models/' . $className . '.php',
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

try {
    // 創建並運行應用程式
    $application = new Yaf_Application(APPLICATION_PATH . '/conf/application.ini', APPLICATION_ENV);
    $application->bootstrap()->run();
} catch (Exception $e) {
    // 錯誤處理
    if (APPLICATION_ENV === 'development') {
        echo '<pre>Error: ' . $e->getMessage() . "\n";
        echo 'File: ' . $e->getFile() . ':' . $e->getLine() . "\n";
        echo 'Trace: ' . $e->getTraceAsString() . '</pre>';
    } else {
        echo '系統發生錯誤，請稍後再試。';
        error_log('Application Error: ' . $e->getMessage());
    }
}
<?php
/**
 * 支付通知處理器
 */
class NotificationHandler
{
    private $db;
    
    public function __construct()
    {
        $this->db = Yaf_Registry::get('db');
    }

    /**
     * 處理支付成功通知
     */
    public function handlePaymentSuccess($orderNo, $transactionId, $paymentData = [])
    {
        $this->db->beginTransaction();
        
        try {
            // 獲取訂單資訊
            $paymentOrderModel = new PaymentOrderModel();
            $order = $paymentOrderModel->findByOrderNo($orderNo);
            
            if (!$order) {
                throw new Exception('訂單不存在: ' . $orderNo);
            }
            
            if ($order['status'] === 'paid') {
                // 訂單已支付，避免重複處理
                $this->db->rollback();
                return true;
            }
            
            // 更新訂單狀態
            $paymentOrderModel->updateOrderStatus($orderNo, 'paid', [
                'transaction_id' => $transactionId,
                'payment_data' => json_encode($paymentData, JSON_UNESCAPED_UNICODE),
                'paid_at' => date('Y-m-d H:i:s')
            ]);
            
            // 處理訂閱
            $this->processSubscription($order, $paymentData);
            
            // 發送通知
            $this->sendPaymentSuccessNotification($order);
            
            // 記錄活動日誌
            $this->logPaymentActivity($order, 'payment_success', $paymentData);
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log('Payment success handling failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 處理支付失敗通知
     */
    public function handlePaymentFailure($orderNo, $reason, $paymentData = [])
    {
        try {
            // 獲取訂單資訊
            $paymentOrderModel = new PaymentOrderModel();
            $order = $paymentOrderModel->findByOrderNo($orderNo);
            
            if (!$order) {
                throw new Exception('訂單不存在: ' . $orderNo);
            }
            
            // 更新訂單狀態
            $paymentOrderModel->updateOrderStatus($orderNo, 'failed', [
                'payment_data' => json_encode(array_merge($paymentData, ['failure_reason' => $reason]), JSON_UNESCAPED_UNICODE)
            ]);
            
            // 發送失敗通知
            $this->sendPaymentFailureNotification($order, $reason);
            
            // 記錄活動日誌
            $this->logPaymentActivity($order, 'payment_failed', ['reason' => $reason]);
            
            return true;
            
        } catch (Exception $e) {
            error_log('Payment failure handling failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 處理訂閱
     */
    private function processSubscription($order, $paymentData)
    {
        $subscriptionModel = new SubscriptionModel();
        $tenantModel = new TenantModel();
        
        // 計算訂閱期間
        $duration = $paymentData['duration_months'] ?? 1;
        
        // 創建訂閱記錄
        $subscription = $subscriptionModel->createSubscription(
            $order['tenant_id'],
            $order['plan_id'],
            $order['id'],
            $duration
        );
        
        // 更新租戶狀態
        $expiresAt = date('Y-m-d H:i:s', strtotime("+{$duration} months"));
        $tenantModel->updateSubscription($order['tenant_id'], $order['plan_id'], $expiresAt);
        
        return $subscription;
    }

    /**
     * 發送支付成功通知
     */
    private function sendPaymentSuccessNotification($order)
    {
        // 獲取租戶資訊
        $tenantModel = new TenantModel();
        $tenant = $tenantModel->find($order['tenant_id']);
        
        if (!$tenant) {
            return;
        }
        
        // 獲取租戶管理員郵箱
        $userModel = new UserModel();
        $stmt = $this->db->prepare("
            SELECT email FROM users 
            WHERE tenant_id = ? AND role = 'owner' AND is_active = 1 
            LIMIT 1
        ");
        $stmt->execute([$order['tenant_id']]);
        $admin = $stmt->fetch();
        
        if (!$admin || !$admin['email']) {
            return;
        }
        
        // 發送郵件通知
        $emailService = new EmailService();
        $emailService->sendPaymentSuccessEmail($admin['email'], [
            'tenant_name' => $tenant['name'],
            'order_no' => $order['order_no'],
            'amount' => $order['amount'],
            'plan_name' => $order['plan_name'],
            'paid_at' => $order['paid_at']
        ]);
    }

    /**
     * 發送支付失敗通知
     */
    private function sendPaymentFailureNotification($order, $reason)
    {
        // 獲取租戶資訊
        $tenantModel = new TenantModel();
        $tenant = $tenantModel->find($order['tenant_id']);
        
        if (!$tenant) {
            return;
        }
        
        // 獲取租戶管理員郵箱
        $userModel = new UserModel();
        $stmt = $this->db->prepare("
            SELECT email FROM users 
            WHERE tenant_id = ? AND role = 'owner' AND is_active = 1 
            LIMIT 1
        ");
        $stmt->execute([$order['tenant_id']]);
        $admin = $stmt->fetch();
        
        if (!$admin || !$admin['email']) {
            return;
        }
        
        // 發送郵件通知
        $emailService = new EmailService();
        $emailService->sendPaymentFailureEmail($admin['email'], [
            'tenant_name' => $tenant['name'],
            'order_no' => $order['order_no'],
            'amount' => $order['amount'],
            'plan_name' => $order['plan_name'],
            'failure_reason' => $reason
        ]);
    }

    /**
     * 記錄支付活動日誌
     */
    private function logPaymentActivity($order, $action, $data)
    {
        $stmt = $this->db->prepare("
            INSERT INTO activity_logs (tenant_id, action, table_name, record_id, new_values, ip_address, created_at)
            VALUES (?, ?, 'payment_orders', ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $order['tenant_id'],
            $action,
            $order['id'],
            json_encode($data, JSON_UNESCAPED_UNICODE),
            $_SERVER['REMOTE_ADDR'] ?? null
        ]);
    }

    /**
     * 處理退款通知
     */
    public function handleRefundNotification($orderNo, $refundAmount, $refundData = [])
    {
        try {
            // 獲取訂單資訊
            $paymentOrderModel = new PaymentOrderModel();
            $order = $paymentOrderModel->findByOrderNo($orderNo);
            
            if (!$order) {
                throw new Exception('訂單不存在: ' . $orderNo);
            }
            
            // 更新訂單狀態
            $status = $refundAmount >= $order['amount'] ? 'refunded' : 'partial_refunded';
            $paymentOrderModel->updateOrderStatus($orderNo, $status, [
                'payment_data' => json_encode(array_merge($refundData, [
                    'refund_amount' => $refundAmount,
                    'refund_at' => date('Y-m-d H:i:s')
                ]), JSON_UNESCAPED_UNICODE)
            ]);
            
            // 如果是全額退款，取消訂閱
            if ($refundAmount >= $order['amount']) {
                $subscriptionModel = new SubscriptionModel();
                $subscriptionModel->cancelSubscription($order['tenant_id']);
            }
            
            // 發送退款通知
            $this->sendRefundNotification($order, $refundAmount);
            
            // 記錄活動日誌
            $this->logPaymentActivity($order, 'refund_processed', [
                'refund_amount' => $refundAmount,
                'refund_data' => $refundData
            ]);
            
            return true;
            
        } catch (Exception $e) {
            error_log('Refund notification handling failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 發送退款通知
     */
    private function sendRefundNotification($order, $refundAmount)
    {
        // 實現退款通知邏輯
        // 可以發送郵件或簡訊通知
    }

    /**
     * 驗證通知來源
     */
    public function verifyNotificationSource($provider, $data)
    {
        $paymentService = PaymentFactory::create($provider);
        
        if (method_exists($paymentService, 'verifyNotification')) {
            return $paymentService->verifyNotification($data);
        }
        
        return true; // 預設通過驗證
    }
}

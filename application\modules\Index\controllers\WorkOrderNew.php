<?php
/**
 * 工單管理控制器
 */
class WorkOrderController extends BaseController
{
    /**
     * 工單列表
     */
    public function indexAction()
    {
        if (!$this->hasPermission('workorder.view')) {
            return $this->error('權限不足', 403);
        }
        
        $page = $this->getParam('page', 1);
        $search = $this->getParam('search', '');
        $status = $this->getParam('status', '');
        $type = $this->getParam('type', '');
        $technicianId = $this->getParam('technician_id', '');
        $perPage = 15;
        
        $workOrderModel = new WorkOrderModel();
        $query = $workOrderModel->query()
            ->select('wo.*, c.name as customer_name, v.license_plate, u.full_name as technician_name')
            ->leftJoin('customers c', 'wo.customer_id = c.id')
            ->leftJoin('vehicles v', 'wo.vehicle_id = v.id')
            ->leftJoin('users u', 'wo.technician_id = u.id');
        
        // 搜索條件
        if (!empty($search)) {
            $query->where("(wo.order_no LIKE ? OR c.name LIKE ? OR v.license_plate LIKE ? OR wo.description LIKE ?)", 
                         "%{$search}%", "%{$search}%", "%{$search}%", "%{$search}%");
        }
        
        // 狀態篩選
        if (!empty($status)) {
            $query->where('wo.status', $status);
        }
        
        // 類型篩選
        if (!empty($type)) {
            $query->where('wo.type', $type);
        }
        
        // 技師篩選
        if (!empty($technicianId)) {
            $query->where('wo.technician_id', $technicianId);
        }
        
        $query->orderBy('wo.created_at', 'DESC');
        
        $result = $this->paginate($query, $page, $perPage);
        
        // 獲取統計數據
        $stats = $workOrderModel->getStats();
        
        // 獲取技師列表
        $userModel = new UserModel();
        $technicians = $userModel->getTechnicians();
        
        $this->getView()->assign('workOrders', $result['data']);
        $this->getView()->assign('pagination', $result['pagination']);
        $this->getView()->assign('search', $search);
        $this->getView()->assign('status', $status);
        $this->getView()->assign('type', $type);
        $this->getView()->assign('technicianId', $technicianId);
        $this->getView()->assign('technicians', $technicians);
        $this->getView()->assign('stats', $stats);
        $this->getView()->assign('title', '工單管理');
    }

    /**
     * 工單詳情
     */
    public function viewAction()
    {
        if (!$this->hasPermission('workorder.view')) {
            return $this->error('權限不足', 403);
        }
        
        $workOrderId = $this->getParam('id');
        if (!$workOrderId) {
            return $this->error('工單ID不能為空', 400);
        }
        
        $workOrderModel = new WorkOrderModel();
        $workOrder = $workOrderModel->getOrderDetails($workOrderId);
        
        if (!$workOrder) {
            return $this->error('工單不存在', 404);
        }
        
        // 獲取活動歷史
        $activityLogModel = new ActivityLogModel();
        $history = $activityLogModel->getRecordHistory('work_orders', $workOrderId);
        
        $this->getView()->assign('workOrder', $workOrder);
        $this->getView()->assign('history', $history);
        $this->getView()->assign('title', '工單詳情 - ' . $workOrder['order_no']);
    }

    /**
     * 新增工單
     */
    public function addAction()
    {
        if (!$this->hasPermission('workorder.create')) {
            return $this->error('權限不足', 403);
        }
        
        if ($this->isPost()) {
            return $this->handleAddWorkOrder();
        }
        
        // 獲取客戶和車輛列表
        $customerModel = new CustomerModel();
        $customers = $customerModel->findAll(['is_active' => 1], 'name ASC');
        
        // 獲取技師列表
        $userModel = new UserModel();
        $technicians = $userModel->getTechnicians();
        
        // 預設值
        $defaultCustomerId = $this->getParam('customer_id');
        $defaultVehicleId = $this->getParam('vehicle_id');
        
        $this->getView()->assign('customers', $customers);
        $this->getView()->assign('technicians', $technicians);
        $this->getView()->assign('defaultCustomerId', $defaultCustomerId);
        $this->getView()->assign('defaultVehicleId', $defaultVehicleId);
        $this->getView()->assign('title', '新增工單');
    }

    /**
     * 處理新增工單
     */
    private function handleAddWorkOrder()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'customer_id' => 'required|integer',
            'vehicle_id' => 'required|integer',
            'type' => 'required|in:maintenance,repair,inspection,other',
            'description' => 'required|max:500',
            'customer_complaint' => 'max:500',
            'mileage_in' => 'integer|min:0',
            'estimated_hours' => 'numeric|min:0',
            'technician_id' => 'integer'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            $this->getView()->assign('old_data', $data);
            return;
        }
        
        try {
            // 檢查客戶和車輛是否存在
            $customerModel = new CustomerModel();
            $customer = $customerModel->find($data['customer_id']);
            if (!$customer) {
                throw new Exception('客戶不存在');
            }
            
            $vehicleModel = new VehicleModel();
            $vehicle = $vehicleModel->find($data['vehicle_id']);
            if (!$vehicle) {
                throw new Exception('車輛不存在');
            }
            
            // 檢查車輛是否屬於該客戶
            if ($vehicle['customer_id'] != $data['customer_id']) {
                throw new Exception('車輛不屬於該客戶');
            }
            
            $workOrderModel = new WorkOrderModel();
            
            $workOrderData = [
                'customer_id' => $data['customer_id'],
                'vehicle_id' => $data['vehicle_id'],
                'technician_id' => $data['technician_id'] ?? null,
                'type' => $data['type'],
                'status' => 'pending',
                'description' => $data['description'],
                'customer_complaint' => $data['customer_complaint'] ?? null,
                'mileage_in' => $data['mileage_in'] ?? null,
                'estimated_hours' => $data['estimated_hours'] ?? null
            ];
            
            $workOrder = $workOrderModel->create($workOrderData);
            
            // 更新車輛里程
            if (!empty($data['mileage_in'])) {
                $vehicleModel->updateMileage($data['vehicle_id'], $data['mileage_in']);
            }
            
            // 記錄操作日誌
            $this->logActivity('work_order_create', 'work_orders', $workOrder['id'], null, $workOrderData);
            
            if ($this->isAjax()) {
                return $this->success(['work_order_id' => $workOrder['id']], '工單新增成功');
            }
            
            return $this->redirect('/workorder/view/' . $workOrder['id'] . '?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('新增失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '新增失敗：' . $e->getMessage());
            $this->getView()->assign('old_data', $data);
        }
    }

    /**
     * 更新工單狀態
     */
    public function updateStatusAction()
    {
        if (!$this->hasPermission('workorder.edit')) {
            return $this->error('權限不足', 403);
        }
        
        if (!$this->isPost()) {
            return $this->error('僅支援 POST 請求', 405);
        }
        
        $workOrderId = $this->getPost('work_order_id');
        $status = $this->getPost('status');
        $notes = $this->getPost('notes', '');
        
        if (!$workOrderId || !$status) {
            return $this->error('參數錯誤', 400);
        }
        
        $allowedStatuses = ['pending', 'in_progress', 'completed', 'cancelled'];
        if (!in_array($status, $allowedStatuses)) {
            return $this->error('無效的狀態', 400);
        }
        
        try {
            $workOrderModel = new WorkOrderModel();
            $workOrder = $workOrderModel->find($workOrderId);
            
            if (!$workOrder) {
                return $this->error('工單不存在', 404);
            }
            
            $additionalData = [];
            if (!empty($notes)) {
                $additionalData['status_notes'] = $notes;
            }
            
            $workOrderModel->updateStatus($workOrderId, $status, $additionalData);
            
            // 記錄操作日誌
            $this->logActivity('work_order_status_change', 'work_orders', $workOrderId, 
                             ['old_status' => $workOrder['status']], 
                             ['new_status' => $status, 'notes' => $notes]);
            
            return $this->success(null, '工單狀態更新成功');
            
        } catch (Exception $e) {
            return $this->error('狀態更新失敗：' . $e->getMessage());
        }
    }

    /**
     * 獲取客戶的車輛列表（AJAX）
     */
    public function getCustomerVehiclesAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        $customerId = $this->getParam('customer_id');
        if (!$customerId) {
            return $this->error('客戶ID不能為空', 400);
        }
        
        $vehicleModel = new VehicleModel();
        $vehicles = $vehicleModel->findAll(['customer_id' => $customerId, 'is_active' => 1], 'license_plate ASC');
        
        return $this->success($vehicles);
    }
}

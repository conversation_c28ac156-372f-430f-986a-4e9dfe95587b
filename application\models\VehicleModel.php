<?php
/**
 * 車輛模型
 */
class VehicleModel extends BaseModel
{
    protected $table = 'vehicles';
    
    protected $fillable = [
        'customer_id', 'license_plate', 'brand', 'model', 'year',
        'engine_no', 'chassis_no', 'color', 'fuel_type', 'mileage', 'notes', 'is_active'
    ];

    /**
     * 搜索車輛
     */
    public function search($query, $limit = 10)
    {
        $sql = "
            SELECT v.*, c.name as customer_name, c.phone as customer_phone
            FROM vehicles v
            LEFT JOIN customers c ON v.customer_id = c.id
            WHERE v.tenant_id = ?
            AND v.is_active = 1
            AND (
                v.license_plate LIKE ? OR
                v.brand LIKE ? OR
                v.model LIKE ? OR
                v.engine_no LIKE ? OR
                v.chassis_no LIKE ? OR
                c.name LIKE ?
            )
            ORDER BY v.license_plate ASC
            LIMIT ?
        ";
        
        $searchTerm = "%{$query}%";
        $tenantId = $this->getCurrentTenantId();
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取車輛詳情
     */
    public function getVehicleDetails($vehicleId)
    {
        $sql = "
            SELECT v.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
            FROM vehicles v
            LEFT JOIN customers c ON v.customer_id = c.id
            WHERE v.id = ? AND v.tenant_id = ?
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$vehicleId, $this->getCurrentTenantId()]);
        $vehicle = $stmt->fetch();
        
        if (!$vehicle) {
            return null;
        }
        
        // 獲取維修記錄
        $workOrderModel = new WorkOrderModel();
        $vehicle['maintenance_history'] = $workOrderModel->getVehicleHistory($vehicleId);
        
        // 獲取最近預約
        $appointmentModel = new AppointmentModel();
        $vehicle['recent_appointments'] = $appointmentModel->getVehicleAppointments($vehicleId, 5);
        
        return $vehicle;
    }

    /**
     * 檢查車牌是否可用
     */
    public function isLicensePlateAvailable($licensePlate, $excludeId = null)
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return false;
        }
        
        $sql = "SELECT COUNT(*) as count FROM vehicles WHERE tenant_id = ? AND license_plate = ?";
        $params = [$tenantId, $licensePlate];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] == 0;
    }

    /**
     * 獲取車輛統計
     */
    public function getStats()
    {
        $tenantId = $this->getCurrentTenantId();
        if (!$tenantId) {
            return [];
        }
        
        $stats = [];
        
        // 總車輛數
        $stats['total'] = $this->count(['is_active' => 1]);
        
        // 本月新增車輛
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM vehicles 
            WHERE tenant_id = ? 
            AND YEAR(created_at) = YEAR(NOW()) 
            AND MONTH(created_at) = MONTH(NOW())
        ");
        $stmt->execute([$tenantId]);
        $stats['monthly_new'] = $stmt->fetch()['count'];
        
        // 按品牌分布
        $stmt = $this->db->prepare("
            SELECT brand, COUNT(*) as count 
            FROM vehicles 
            WHERE tenant_id = ? AND is_active = 1 AND brand IS NOT NULL
            GROUP BY brand
            ORDER BY count DESC
            LIMIT 10
        ");
        $stmt->execute([$tenantId]);
        $stats['by_brand'] = $stmt->fetchAll();
        
        // 按燃料類型分布
        $stmt = $this->db->prepare("
            SELECT fuel_type, COUNT(*) as count 
            FROM vehicles 
            WHERE tenant_id = ? AND is_active = 1 AND fuel_type IS NOT NULL
            GROUP BY fuel_type
        ");
        $stmt->execute([$tenantId]);
        $fuelStats = $stmt->fetchAll();
        $stats['by_fuel_type'] = [];
        foreach ($fuelStats as $stat) {
            $stats['by_fuel_type'][$stat['fuel_type']] = $stat['count'];
        }
        
        // 按年份分布
        $stmt = $this->db->prepare("
            SELECT 
                CASE 
                    WHEN year >= YEAR(NOW()) - 3 THEN 'new'
                    WHEN year >= YEAR(NOW()) - 10 THEN 'medium'
                    ELSE 'old'
                END as age_group,
                COUNT(*) as count
            FROM vehicles 
            WHERE tenant_id = ? AND is_active = 1 AND year IS NOT NULL
            GROUP BY age_group
        ");
        $stmt->execute([$tenantId]);
        $ageStats = $stmt->fetchAll();
        $stats['by_age'] = [];
        foreach ($ageStats as $stat) {
            $stats['by_age'][$stat['age_group']] = $stat['count'];
        }
        
        return $stats;
    }

    /**
     * 獲取車輛維修頻率
     */
    public function getMaintenanceFrequency($vehicleId, $months = 12)
    {
        $stmt = $this->db->prepare("
            SELECT 
                DATE_FORMAT(wo.created_at, '%Y-%m') as month,
                COUNT(*) as order_count,
                SUM(wo.final_amount) as total_cost
            FROM work_orders wo
            WHERE wo.vehicle_id = ?
            AND wo.created_at >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            GROUP BY DATE_FORMAT(wo.created_at, '%Y-%m')
            ORDER BY month DESC
        ");
        $stmt->execute([$vehicleId, $months]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取需要保養的車輛
     */
    public function getVehiclesNeedingMaintenance($mileageThreshold = 5000, $timeThreshold = 6)
    {
        $sql = "
            SELECT v.*, c.name as customer_name, c.phone as customer_phone,
                   wo.completed_at as last_maintenance,
                   TIMESTAMPDIFF(MONTH, wo.completed_at, NOW()) as months_since_maintenance
            FROM vehicles v
            LEFT JOIN customers c ON v.customer_id = c.id
            LEFT JOIN (
                SELECT vehicle_id, MAX(completed_at) as completed_at
                FROM work_orders 
                WHERE status = 'completed' AND type = 'maintenance'
                GROUP BY vehicle_id
            ) wo ON v.id = wo.vehicle_id
            WHERE v.tenant_id = ?
            AND v.is_active = 1
            AND (
                wo.completed_at IS NULL OR
                TIMESTAMPDIFF(MONTH, wo.completed_at, NOW()) >= ?
            )
            ORDER BY wo.completed_at ASC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$this->getCurrentTenantId(), $timeThreshold]);
        
        return $stmt->fetchAll();
    }

    /**
     * 更新車輛里程
     */
    public function updateMileage($vehicleId, $newMileage)
    {
        $vehicle = $this->find($vehicleId);
        if (!$vehicle) {
            return false;
        }
        
        // 只允許增加里程，不允許減少
        if ($newMileage < $vehicle['mileage']) {
            throw new Exception('新里程不能小於當前里程');
        }
        
        return $this->update($vehicleId, ['mileage' => $newMileage]);
    }

    /**
     * 獲取車輛的最新里程（從工單中）
     */
    public function getLatestMileageFromOrders($vehicleId)
    {
        $stmt = $this->db->prepare("
            SELECT mileage_out 
            FROM work_orders 
            WHERE vehicle_id = ? 
            AND mileage_out IS NOT NULL 
            ORDER BY completed_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$vehicleId]);
        $result = $stmt->fetch();
        
        return $result ? $result['mileage_out'] : null;
    }

    /**
     * 檢查車輛是否可以刪除
     */
    public function canDelete($vehicleId)
    {
        // 檢查是否有關聯的工單
        $orderCount = $this->db->prepare("SELECT COUNT(*) as count FROM work_orders WHERE vehicle_id = ?");
        $orderCount->execute([$vehicleId]);
        if ($orderCount->fetch()['count'] > 0) {
            return false;
        }
        
        // 檢查是否有關聯的預約
        $appointmentCount = $this->db->prepare("SELECT COUNT(*) as count FROM appointments WHERE vehicle_id = ?");
        $appointmentCount->execute([$vehicleId]);
        if ($appointmentCount->fetch()['count'] > 0) {
            return false;
        }
        
        return true;
    }
}

// 續租處理
function renewSubscription(planId) {
    fetch('/payment/renew', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            plan_id: planId,
            tenant_id: getCurrentTenantId()
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.payment_url) {
            window.location.href = data.payment_url;
        }
    });
}
<?php
/**
 * 多租戶插件
 * 負責處理租戶識別和數據隔離
 */
class TenantPlugin extends Yaf_Plugin_Abstract
{
    /**
     * 路由前處理
     */
    public function routerStartup(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response)
    {
        // 設定當前租戶
        $this->setCurrentTenant($request);
    }

    /**
     * 路由後處理
     */
    public function routerShutdown(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response)
    {
        // 檢查租戶權限
        $this->checkTenantPermission($request);
    }

    /**
     * 控制器前處理
     */
    public function preDispatch(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response)
    {
        // 檢查用戶登入狀態
        $this->checkUserAuth($request, $response);
    }

    /**
     * 設定當前租戶
     */
    private function setCurrentTenant(Yaf_Request_Abstract $request)
    {
        $tenantId = null;
        $subdomain = null;
        
        // 從 URL 路徑獲取租戶資訊
        if ($request->getModuleName() === 'Tenant') {
            $subdomain = $request->getParam('subdomain');
        }
        
        // 從 session 獲取租戶資訊
        if (!$subdomain && isset($_SESSION['tenant_id'])) {
            $tenantId = $_SESSION['tenant_id'];
        }
        
        // 從子域名獲取租戶資訊
        if (!$subdomain && !$tenantId) {
            $host = $_SERVER['HTTP_HOST'] ?? '';
            $parts = explode('.', $host);
            if (count($parts) > 2) {
                $subdomain = $parts[0];
            }
        }
        
        // 查詢租戶資訊
        if ($subdomain || $tenantId) {
            $tenant = $this->getTenantInfo($subdomain, $tenantId);
            if ($tenant) {
                // 設定當前租戶到全域變數
                Yaf_Registry::set('current_tenant', $tenant);
                $_SESSION['tenant_id'] = $tenant['id'];
                
                // 檢查租戶狀態
                $this->checkTenantStatus($tenant);
            }
        }
    }

    /**
     * 獲取租戶資訊
     */
    private function getTenantInfo($subdomain = null, $tenantId = null)
    {
        $db = Yaf_Registry::get('db');
        
        if ($subdomain) {
            $stmt = $db->prepare("
                SELECT t.*, sp.name as plan_name, sp.max_users, sp.max_vehicles 
                FROM tenants t 
                LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id 
                WHERE t.subdomain = ? AND t.status != 'cancelled'
            ");
            $stmt->execute([$subdomain]);
        } elseif ($tenantId) {
            $stmt = $db->prepare("
                SELECT t.*, sp.name as plan_name, sp.max_users, sp.max_vehicles 
                FROM tenants t 
                LEFT JOIN subscription_plans sp ON t.current_plan_id = sp.id 
                WHERE t.id = ? AND t.status != 'cancelled'
            ");
            $stmt->execute([$tenantId]);
        } else {
            return null;
        }
        
        return $stmt->fetch();
    }

    /**
     * 檢查租戶狀態
     */
    private function checkTenantStatus($tenant)
    {
        $now = new DateTime();
        
        // 檢查試用期
        if ($tenant['status'] === 'trial') {
            $trialExpires = new DateTime($tenant['trial_expires_at']);
            if ($now > $trialExpires) {
                // 試用期已過期
                $this->redirectToPayment('trial_expired');
                return;
            }
        }
        
        // 檢查訂閱期
        if ($tenant['subscription_expires_at']) {
            $subscriptionExpires = new DateTime($tenant['subscription_expires_at']);
            if ($now > $subscriptionExpires) {
                // 訂閱已過期
                if ($tenant['status'] !== 'suspended') {
                    $this->suspendTenant($tenant['id']);
                }
                $this->redirectToPayment('subscription_expired');
                return;
            }
        }
        
        // 檢查暫停狀態
        if ($tenant['status'] === 'suspended') {
            $this->redirectToPayment('account_suspended');
            return;
        }
    }

    /**
     * 暫停租戶
     */
    private function suspendTenant($tenantId)
    {
        $db = Yaf_Registry::get('db');
        $stmt = $db->prepare("UPDATE tenants SET status = 'suspended' WHERE id = ?");
        $stmt->execute([$tenantId]);
    }

    /**
     * 重定向到支付頁面
     */
    private function redirectToPayment($reason)
    {
        $url = '/payment/subscribe?reason=' . $reason;
        header('Location: ' . $url);
        exit;
    }

    /**
     * 檢查租戶權限
     */
    private function checkTenantPermission(Yaf_Request_Abstract $request)
    {
        $tenant = Yaf_Registry::get('current_tenant');
        
        // 如果是公開頁面，不需要檢查
        $publicPages = ['Index', 'Payment', 'Auth'];
        if (in_array($request->getControllerName(), $publicPages)) {
            return;
        }
        
        // 如果沒有租戶資訊，重定向到首頁
        if (!$tenant) {
            header('Location: /');
            exit;
        }
    }

    /**
     * 檢查用戶認證
     */
    private function checkUserAuth(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response)
    {
        // 不需要認證的頁面
        $publicActions = [
            'Index' => ['index', 'login', 'register'],
            'Auth' => ['login', 'register', 'logout'],
            'Payment' => ['notify', 'return']
        ];
        
        $controller = $request->getControllerName();
        $action = $request->getActionName();
        
        if (isset($publicActions[$controller]) && in_array($action, $publicActions[$controller])) {
            return;
        }
        
        // 檢查用戶是否已登入
        if (!isset($_SESSION['user_id'])) {
            if ($request->isXmlHttpRequest()) {
                // AJAX 請求返回 JSON
                $response->setHeader('Content-Type', 'application/json');
                $response->setBody(json_encode([
                    'success' => false,
                    'message' => '請先登入',
                    'redirect' => '/auth/login'
                ]));
                return false;
            } else {
                // 一般請求重定向到登入頁面
                header('Location: /auth/login');
                exit;
            }
        }
        
        // 設定當前用戶資訊
        $this->setCurrentUser();
    }

    /**
     * 設定當前用戶
     */
    private function setCurrentUser()
    {
        if (!isset($_SESSION['user_id'])) {
            return;
        }
        
        $db = Yaf_Registry::get('db');
        $stmt = $db->prepare("
            SELECT u.*, t.name as tenant_name 
            FROM users u 
            LEFT JOIN tenants t ON u.tenant_id = t.id 
            WHERE u.id = ? AND u.is_active = 1
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        
        if ($user) {
            Yaf_Registry::set('current_user', $user);
            
            // 更新最後登入時間
            $updateStmt = $db->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
            $updateStmt->execute([$user['id']]);
        } else {
            // 用戶不存在或已停用，清除 session
            session_destroy();
            header('Location: /auth/login');
            exit;
        }
    }
}

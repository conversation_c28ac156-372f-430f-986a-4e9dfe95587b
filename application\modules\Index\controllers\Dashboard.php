<?php
/**
 * 儀表板控制器
 */
class DashboardController extends BaseController
{
    /**
     * 主儀表板
     */
    public function indexAction()
    {
        if (!$this->user) {
            return $this->redirect('/auth/login');
        }
        
        // 獲取各種統計數據
        $stats = $this->getDashboardStats();
        
        // 獲取今日預約
        $appointmentModel = new AppointmentModel();
        $todayAppointments = $appointmentModel->getTodayAppointments(5);
        
        // 獲取待處理工單
        $workOrderModel = new WorkOrderModel();
        $pendingOrders = $workOrderModel->getPendingOrders(5);
        
        // 獲取低庫存零件
        $partModel = new PartModel();
        $lowStockParts = $partModel->getLowStockParts(5);
        
        // 獲取最近活動
        $activityLogModel = new ActivityLogModel();
        $recentActivities = $activityLogModel->getActivityLogs([], 1, 10);
        
        // 獲取收入趨勢（最近30天）
        $paymentOrderModel = new PaymentOrderModel();
        $revenueTrend = $paymentOrderModel->getRevenueTrend(30);
        
        $this->getView()->assign('stats', $stats);
        $this->getView()->assign('todayAppointments', $todayAppointments);
        $this->getView()->assign('pendingOrders', $pendingOrders);
        $this->getView()->assign('lowStockParts', $lowStockParts);
        $this->getView()->assign('recentActivities', $recentActivities);
        $this->getView()->assign('revenueTrend', $revenueTrend);
        $this->getView()->assign('title', '儀表板');
    }

    /**
     * 獲取儀表板統計數據
     */
    private function getDashboardStats()
    {
        $stats = [];
        
        // 客戶統計
        $customerModel = new CustomerModel();
        $stats['customers'] = [
            'total' => $customerModel->count(['is_active' => 1]),
            'new_this_month' => $customerModel->count([
                'is_active' => 1,
                'created_at >=' => date('Y-m-01 00:00:00')
            ])
        ];
        
        // 車輛統計
        $vehicleModel = new VehicleModel();
        $stats['vehicles'] = [
            'total' => $vehicleModel->count(['is_active' => 1]),
            'new_this_month' => $vehicleModel->count([
                'is_active' => 1,
                'created_at >=' => date('Y-m-01 00:00:00')
            ])
        ];
        
        // 工單統計
        $workOrderModel = new WorkOrderModel();
        $stats['work_orders'] = [
            'total' => $workOrderModel->count(),
            'pending' => $workOrderModel->count(['status' => 'pending']),
            'in_progress' => $workOrderModel->count(['status' => 'in_progress']),
            'completed_today' => $workOrderModel->count([
                'status' => 'completed',
                'completed_at >=' => date('Y-m-d 00:00:00')
            ])
        ];
        
        // 預約統計
        $appointmentModel = new AppointmentModel();
        $stats['appointments'] = [
            'today' => $appointmentModel->count([
                'appointment_date' => date('Y-m-d'),
                'status' => 'confirmed'
            ]),
            'this_week' => $appointmentModel->count([
                'appointment_date >=' => date('Y-m-d'),
                'appointment_date <=' => date('Y-m-d', strtotime('+7 days')),
                'status' => 'confirmed'
            ])
        ];
        
        // 零件統計
        $partModel = new PartModel();
        $stats['parts'] = [
            'total' => $partModel->count(['is_active' => 1]),
            'low_stock' => $partModel->count([
                'is_active' => 1,
                'stock_quantity <= min_stock'
            ]),
            'out_of_stock' => $partModel->count([
                'is_active' => 1,
                'stock_quantity' => 0
            ])
        ];
        
        // 收入統計（本月）
        $paymentOrderModel = new PaymentOrderModel();
        $monthlyRevenue = $paymentOrderModel->db->prepare("
            SELECT COALESCE(SUM(amount), 0) as revenue
            FROM payment_orders
            WHERE tenant_id = ?
            AND status = 'paid'
            AND YEAR(created_at) = YEAR(NOW())
            AND MONTH(created_at) = MONTH(NOW())
        ");
        $monthlyRevenue->execute([$this->tenant['id']]);
        $stats['revenue'] = [
            'this_month' => $monthlyRevenue->fetch()['revenue']
        ];
        
        return $stats;
    }

    /**
     * 系統狀態
     */
    public function statusAction()
    {
        if (!$this->hasPermission('system.view')) {
            return $this->error('權限不足', 403);
        }
        
        $status = [
            'system' => [
                'php_version' => PHP_VERSION,
                'memory_usage' => memory_get_usage(true),
                'memory_limit' => ini_get('memory_limit'),
                'disk_space' => disk_free_space('.'),
                'server_time' => date('Y-m-d H:i:s')
            ],
            'database' => $this->getDatabaseStatus(),
            'cache' => $this->getCacheStatus(),
            'storage' => $this->getStorageStatus()
        ];
        
        $this->getView()->assign('status', $status);
        $this->getView()->assign('title', '系統狀態');
    }

    /**
     * 獲取資料庫狀態
     */
    private function getDatabaseStatus()
    {
        try {
            $db = Yaf_Registry::get('db');
            
            // 檢查連接
            $stmt = $db->prepare("SELECT 1");
            $stmt->execute();
            
            // 獲取資料庫大小
            $sizeStmt = $db->prepare("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            $sizeStmt->execute();
            $size = $sizeStmt->fetch();
            
            return [
                'status' => 'connected',
                'size_mb' => $size['size_mb'] ?? 0,
                'version' => $db->getAttribute(PDO::ATTR_SERVER_VERSION)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 獲取快取狀態
     */
    private function getCacheStatus()
    {
        // 這裡可以檢查 Redis 或其他快取系統
        return [
            'status' => 'not_configured',
            'type' => 'file'
        ];
    }

    /**
     * 獲取儲存狀態
     */
    private function getStorageStatus()
    {
        $uploadDir = APPLICATION_PATH . '/../public/uploads';
        
        return [
            'upload_dir' => $uploadDir,
            'writable' => is_writable($uploadDir),
            'free_space' => disk_free_space($uploadDir)
        ];
    }

    /**
     * 快速統計（AJAX）
     */
    public function quickStatsAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        $stats = $this->getDashboardStats();
        return $this->success($stats);
    }

    /**
     * 搜索（全域搜索）
     */
    public function searchAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        $query = $this->getParam('q', '');
        $limit = $this->getParam('limit', 5);
        
        if (strlen($query) < 2) {
            return $this->success([]);
        }
        
        $results = [];
        
        // 搜索客戶
        if ($this->hasPermission('customer.view')) {
            $customerModel = new CustomerModel();
            $customers = $customerModel->search($query, $limit);
            foreach ($customers as $customer) {
                $results[] = [
                    'type' => 'customer',
                    'id' => $customer['id'],
                    'title' => $customer['name'],
                    'subtitle' => $customer['phone'] ?? $customer['mobile'],
                    'url' => '/customer/view/' . $customer['id']
                ];
            }
        }
        
        // 搜索車輛
        if ($this->hasPermission('vehicle.view')) {
            $vehicleModel = new VehicleModel();
            $vehicles = $vehicleModel->search($query, $limit);
            foreach ($vehicles as $vehicle) {
                $results[] = [
                    'type' => 'vehicle',
                    'id' => $vehicle['id'],
                    'title' => $vehicle['license_plate'],
                    'subtitle' => $vehicle['brand'] . ' ' . $vehicle['model'],
                    'url' => '/vehicle/view/' . $vehicle['id']
                ];
            }
        }
        
        // 搜索工單
        if ($this->hasPermission('workorder.view')) {
            $workOrderModel = new WorkOrderModel();
            $workOrders = $workOrderModel->search($query, $limit);
            foreach ($workOrders as $workOrder) {
                $results[] = [
                    'type' => 'work_order',
                    'id' => $workOrder['id'],
                    'title' => $workOrder['order_no'],
                    'subtitle' => $workOrder['customer_name'] . ' - ' . $workOrder['license_plate'],
                    'url' => '/workorder/view/' . $workOrder['id']
                ];
            }
        }
        
        // 搜索零件
        if ($this->hasPermission('part.view')) {
            $partModel = new PartModel();
            $parts = $partModel->search($query, $limit);
            foreach ($parts as $part) {
                $results[] = [
                    'type' => 'part',
                    'id' => $part['id'],
                    'title' => $part['name'],
                    'subtitle' => $part['part_no'] . ' - 庫存: ' . $part['stock_quantity'],
                    'url' => '/part/view/' . $part['id']
                ];
            }
        }
        
        return $this->success($results);
    }

    /**
     * 通知中心
     */
    public function notificationsAction()
    {
        $notifications = [];
        
        // 低庫存提醒
        if ($this->hasPermission('part.view')) {
            $partModel = new PartModel();
            $lowStockParts = $partModel->getLowStockParts(10);
            foreach ($lowStockParts as $part) {
                $notifications[] = [
                    'type' => 'warning',
                    'title' => '低庫存提醒',
                    'message' => $part['name'] . ' 庫存不足（剩餘: ' . $part['stock_quantity'] . '）',
                    'url' => '/part/view/' . $part['id'],
                    'time' => date('Y-m-d H:i:s')
                ];
            }
        }
        
        // 今日預約提醒
        if ($this->hasPermission('appointment.view')) {
            $appointmentModel = new AppointmentModel();
            $todayAppointments = $appointmentModel->getTodayAppointments();
            if (count($todayAppointments) > 0) {
                $notifications[] = [
                    'type' => 'info',
                    'title' => '今日預約',
                    'message' => '今日有 ' . count($todayAppointments) . ' 個預約',
                    'url' => '/appointment/today',
                    'time' => date('Y-m-d H:i:s')
                ];
            }
        }
        
        // 待處理工單提醒
        if ($this->hasPermission('workorder.view')) {
            $workOrderModel = new WorkOrderModel();
            $pendingOrders = $workOrderModel->getPendingOrders();
            if (count($pendingOrders) > 0) {
                $notifications[] = [
                    'type' => 'warning',
                    'title' => '待處理工單',
                    'message' => '有 ' . count($pendingOrders) . ' 個工單待處理',
                    'url' => '/workorder?status=pending',
                    'time' => date('Y-m-d H:i:s')
                ];
            }
        }
        
        if ($this->isAjax()) {
            return $this->success($notifications);
        }
        
        $this->getView()->assign('notifications', $notifications);
        $this->getView()->assign('title', '通知中心');
    }

    /**
     * 個人設定
     */
    public function profileAction()
    {
        if ($this->isPost()) {
            return $this->handleUpdateProfile();
        }
        
        $this->getView()->assign('user', $this->user);
        $this->getView()->assign('title', '個人設定');
    }

    /**
     * 處理個人設定更新
     */
    private function handleUpdateProfile()
    {
        $data = $this->getPost();
        
        // 驗證輸入
        $validation = $this->validate($data, [
            'full_name' => 'required|max:100',
            'email' => 'required|email|max:100',
            'phone' => 'phone',
            'current_password' => 'required_with:new_password',
            'new_password' => 'min:6',
            'new_password_confirmation' => 'required_with:new_password|confirmed'
        ]);
        
        if (!$validation['valid']) {
            if ($this->isAjax()) {
                return $this->error('請檢查輸入資料', 400, $validation['errors']);
            }
            $this->getView()->assign('errors', $validation['errors']);
            return;
        }
        
        try {
            $userModel = new UserModel();
            
            // 檢查郵箱是否可用
            if (!$userModel->isEmailAvailable($data['email'], $this->user['id'])) {
                throw new Exception('郵箱已被使用');
            }
            
            $updateData = [
                'full_name' => $data['full_name'],
                'email' => $data['email'],
                'phone' => $data['phone'] ?? null
            ];
            
            // 如果要更改密碼
            if (!empty($data['new_password'])) {
                // 驗證當前密碼
                if (!$userModel->verifyPassword($this->user['id'], $data['current_password'])) {
                    throw new Exception('當前密碼錯誤');
                }
                $updateData['password'] = $data['new_password'];
            }
            
            $userModel->update($this->user['id'], $updateData);
            
            // 更新 session 中的用戶資訊
            $updatedUser = $userModel->find($this->user['id']);
            $_SESSION['user'] = $updatedUser;
            Yaf_Registry::set('current_user', $updatedUser);
            
            // 記錄操作日誌
            $this->logActivity('profile_update', 'users', $this->user['id'], $this->user, $updateData);
            
            if ($this->isAjax()) {
                return $this->success(null, '個人設定更新成功');
            }
            
            return $this->redirect('/dashboard/profile?success=1');
            
        } catch (Exception $e) {
            if ($this->isAjax()) {
                return $this->error('更新失敗：' . $e->getMessage());
            }
            $this->getView()->assign('error', '更新失敗：' . $e->getMessage());
        }
    }
}

<?php
/**
 * 訂閱模型
 */
class SubscriptionModel extends BaseModel
{
    protected $table = 'subscriptions';
    protected $tenantField = null; // 訂閱記錄不需要租戶過濾，因為它本身就是租戶相關的
    
    protected $fillable = [
        'tenant_id', 'plan_id', 'order_id', 'starts_at', 'expires_at', 'status'
    ];

    /**
     * 獲取租戶的訂閱記錄
     */
    public function getTenantSubscriptions($tenantId, $limit = null)
    {
        $sql = "
            SELECT s.*, sp.name as plan_name, sp.price, po.order_no
            FROM subscriptions s
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
            LEFT JOIN payment_orders po ON s.order_id = po.id
            WHERE s.tenant_id = ?
            ORDER BY s.created_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取當前有效訂閱
     */
    public function getCurrentSubscription($tenantId)
    {
        $sql = "
            SELECT s.*, sp.name as plan_name, sp.price, sp.max_users, sp.max_vehicles
            FROM subscriptions s
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
            WHERE s.tenant_id = ?
            AND s.status = 'active'
            AND s.expires_at > NOW()
            ORDER BY s.expires_at DESC
            LIMIT 1
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$tenantId]);
        
        return $stmt->fetch();
    }

    /**
     * 創建訂閱記錄
     */
    public function createSubscription($tenantId, $planId, $orderId, $duration = 1)
    {
        // 獲取當前訂閱的到期時間
        $currentSubscription = $this->getCurrentSubscription($tenantId);
        
        if ($currentSubscription && strtotime($currentSubscription['expires_at']) > time()) {
            // 如果有有效訂閱，從到期時間開始計算
            $startsAt = $currentSubscription['expires_at'];
        } else {
            // 否則從現在開始
            $startsAt = date('Y-m-d H:i:s');
        }
        
        $expiresAt = date('Y-m-d H:i:s', strtotime($startsAt . " +{$duration} months"));
        
        $data = [
            'tenant_id' => $tenantId,
            'plan_id' => $planId,
            'order_id' => $orderId,
            'starts_at' => $startsAt,
            'expires_at' => $expiresAt,
            'status' => 'active'
        ];
        
        return $this->create($data);
    }

    /**
     * 延長訂閱
     */
    public function extendSubscription($tenantId, $planId, $orderId, $duration = 1)
    {
        return $this->createSubscription($tenantId, $planId, $orderId, $duration);
    }

    /**
     * 取消訂閱
     */
    public function cancelSubscription($tenantId)
    {
        $sql = "
            UPDATE subscriptions 
            SET status = 'cancelled' 
            WHERE tenant_id = ? 
            AND status = 'active'
        ";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$tenantId]);
    }

    /**
     * 獲取即將到期的訂閱
     */
    public function getExpiringSubscriptions($days = 7)
    {
        $sql = "
            SELECT s.*, t.name as tenant_name, t.subdomain, sp.name as plan_name
            FROM subscriptions s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
            WHERE s.status = 'active'
            AND s.expires_at <= DATE_ADD(NOW(), INTERVAL ? DAY)
            AND s.expires_at > NOW()
            ORDER BY s.expires_at ASC
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$days]);
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取已過期的訂閱
     */
    public function getExpiredSubscriptions()
    {
        $sql = "
            UPDATE subscriptions 
            SET status = 'expired' 
            WHERE status = 'active' 
            AND expires_at <= NOW()
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        // 返回剛剛過期的訂閱
        $sql = "
            SELECT s.*, t.name as tenant_name, t.subdomain, sp.name as plan_name
            FROM subscriptions s
            LEFT JOIN tenants t ON s.tenant_id = t.id
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
            WHERE s.status = 'expired'
            AND s.expires_at <= NOW()
            AND s.expires_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * 獲取訂閱統計
     */
    public function getStats()
    {
        $stats = [];
        
        // 總訂閱數
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM subscriptions");
        $stmt->execute();
        $stats['total'] = $stmt->fetch()['count'];
        
        // 活躍訂閱數
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM subscriptions 
            WHERE status = 'active' AND expires_at > NOW()
        ");
        $stmt->execute();
        $stats['active'] = $stmt->fetch()['count'];
        
        // 本月新增訂閱
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM subscriptions 
            WHERE YEAR(created_at) = YEAR(NOW()) 
            AND MONTH(created_at) = MONTH(NOW())
        ");
        $stmt->execute();
        $stats['monthly_new'] = $stmt->fetch()['count'];
        
        // 按方案統計
        $stmt = $this->db->prepare("
            SELECT sp.name as plan_name, COUNT(s.id) as count
            FROM subscriptions s
            LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
            WHERE s.status = 'active' AND s.expires_at > NOW()
            GROUP BY s.plan_id, sp.name
            ORDER BY count DESC
        ");
        $stmt->execute();
        $stats['by_plan'] = $stmt->fetchAll();
        
        // 本月收入
        $stmt = $this->db->prepare("
            SELECT COALESCE(SUM(po.amount), 0) as revenue
            FROM subscriptions s
            LEFT JOIN payment_orders po ON s.order_id = po.id
            WHERE po.status = 'paid'
            AND YEAR(s.created_at) = YEAR(NOW()) 
            AND MONTH(s.created_at) = MONTH(NOW())
        ");
        $stmt->execute();
        $stats['monthly_revenue'] = $stmt->fetch()['revenue'];
        
        // 續約率（過去3個月）
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(CASE WHEN renewal_count > 0 THEN 1 END) as renewed,
                COUNT(*) as total
            FROM (
                SELECT tenant_id, COUNT(*) - 1 as renewal_count
                FROM subscriptions
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
                GROUP BY tenant_id
            ) as renewal_stats
        ");
        $stmt->execute();
        $renewalStats = $stmt->fetch();
        $stats['renewal_rate'] = $renewalStats['total'] > 0 ? 
            round(($renewalStats['renewed'] / $renewalStats['total']) * 100, 2) : 0;
        
        return $stats;
    }

    /**
     * 獲取收入趨勢
     */
    public function getRevenueTrend($months = 12)
    {
        $stmt = $this->db->prepare("
            SELECT 
                DATE_FORMAT(s.created_at, '%Y-%m') as month,
                COUNT(s.id) as subscription_count,
                COALESCE(SUM(po.amount), 0) as revenue
            FROM subscriptions s
            LEFT JOIN payment_orders po ON s.order_id = po.id
            WHERE s.created_at >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            AND po.status = 'paid'
            GROUP BY DATE_FORMAT(s.created_at, '%Y-%m')
            ORDER BY month ASC
        ");
        $stmt->execute([$months]);
        
        return $stmt->fetchAll();
    }

    /**
     * 檢查租戶是否有有效訂閱
     */
    public function hasValidSubscription($tenantId)
    {
        $subscription = $this->getCurrentSubscription($tenantId);
        return $subscription && strtotime($subscription['expires_at']) > time();
    }

    /**
     * 獲取訂閱剩餘天數
     */
    public function getRemainingDays($tenantId)
    {
        $subscription = $this->getCurrentSubscription($tenantId);
        if (!$subscription) {
            return 0;
        }
        
        $expiresAt = new DateTime($subscription['expires_at']);
        $now = new DateTime();
        
        if ($expiresAt <= $now) {
            return 0;
        }
        
        $diff = $expiresAt->diff($now);
        return $diff->days;
    }

    /**
     * 自動續約處理
     */
    public function processAutoRenewal($tenantId)
    {
        // 檢查租戶是否開啟自動續約
        $tenantModel = new TenantModel();
        $tenant = $tenantModel->find($tenantId);
        
        if (!$tenant || !$tenant['auto_renew']) {
            return false;
        }
        
        $currentSubscription = $this->getCurrentSubscription($tenantId);
        if (!$currentSubscription) {
            return false;
        }
        
        // 檢查是否即將到期（7天內）
        $expiresAt = new DateTime($currentSubscription['expires_at']);
        $now = new DateTime();
        $diff = $expiresAt->diff($now);
        
        if ($diff->days > 7) {
            return false;
        }
        
        try {
            // 創建自動續約訂單
            $paymentModel = new PaymentOrderModel();
            $planModel = new SubscriptionPlanModel();
            $plan = $planModel->find($currentSubscription['plan_id']);
            
            $orderData = [
                'tenant_id' => $tenantId,
                'plan_id' => $plan['id'],
                'amount' => $plan['price'],
                'auto_renewal' => true
            ];
            
            $order = $paymentModel->createOrder($orderData);
            
            // 這裡應該調用支付API進行自動扣款
            // 如果成功，創建新的訂閱記錄
            
            return $order;
            
        } catch (Exception $e) {
            error_log('Auto renewal failed for tenant ' . $tenantId . ': ' . $e->getMessage());
            return false;
        }
    }
}

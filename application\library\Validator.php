<?php
/**
 * 數據驗證器
 * 提供常用的數據驗證功能
 */
class Validator
{
    private $errors = [];

    /**
     * 驗證數據
     */
    public function validate($data, $rules)
    {
        $this->errors = [];
        
        foreach ($rules as $field => $ruleSet) {
            $value = $data[$field] ?? null;
            $fieldRules = explode('|', $ruleSet);
            
            foreach ($fieldRules as $rule) {
                $this->validateField($field, $value, $rule, $data);
            }
        }
        
        return [
            'valid' => empty($this->errors),
            'errors' => $this->errors
        ];
    }

    /**
     * 驗證單個字段
     */
    private function validateField($field, $value, $rule, $allData)
    {
        $parts = explode(':', $rule);
        $ruleName = $parts[0];
        $ruleValue = $parts[1] ?? null;
        
        switch ($ruleName) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    $this->addError($field, "{$field} 為必填項目");
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->addError($field, "{$field} 必須是有效的電子郵件地址");
                }
                break;
                
            case 'min':
                if (!empty($value) && strlen($value) < $ruleValue) {
                    $this->addError($field, "{$field} 最少需要 {$ruleValue} 個字符");
                }
                break;
                
            case 'max':
                if (!empty($value) && strlen($value) > $ruleValue) {
                    $this->addError($field, "{$field} 最多只能 {$ruleValue} 個字符");
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    $this->addError($field, "{$field} 必須是數字");
                }
                break;
                
            case 'integer':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                    $this->addError($field, "{$field} 必須是整數");
                }
                break;
                
            case 'phone':
                if (!empty($value) && !$this->validatePhone($value)) {
                    $this->addError($field, "{$field} 必須是有效的電話號碼");
                }
                break;
                
            case 'mobile':
                if (!empty($value) && !$this->validateMobile($value)) {
                    $this->addError($field, "{$field} 必須是有效的手機號碼");
                }
                break;
                
            case 'id_number':
                if (!empty($value) && !$this->validateIdNumber($value)) {
                    $this->addError($field, "{$field} 必須是有效的身分證字號");
                }
                break;
                
            case 'license_plate':
                if (!empty($value) && !$this->validateLicensePlate($value)) {
                    $this->addError($field, "{$field} 必須是有效的車牌號碼");
                }
                break;
                
            case 'date':
                if (!empty($value) && !$this->validateDate($value)) {
                    $this->addError($field, "{$field} 必須是有效的日期格式 (YYYY-MM-DD)");
                }
                break;
                
            case 'datetime':
                if (!empty($value) && !$this->validateDateTime($value)) {
                    $this->addError($field, "{$field} 必須是有效的日期時間格式 (YYYY-MM-DD HH:MM:SS)");
                }
                break;
                
            case 'in':
                $allowedValues = explode(',', $ruleValue);
                if (!empty($value) && !in_array($value, $allowedValues)) {
                    $this->addError($field, "{$field} 必須是以下值之一: " . implode(', ', $allowedValues));
                }
                break;
                
            case 'unique':
                if (!empty($value) && !$this->validateUnique($field, $value, $ruleValue, $allData)) {
                    $this->addError($field, "{$field} 已經存在");
                }
                break;
                
            case 'confirmed':
                $confirmField = $field . '_confirmation';
                if (!empty($value) && $value !== ($allData[$confirmField] ?? null)) {
                    $this->addError($field, "{$field} 確認不匹配");
                }
                break;
        }
    }

    /**
     * 添加錯誤訊息
     */
    private function addError($field, $message)
    {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }

    /**
     * 驗證電話號碼
     */
    private function validatePhone($phone)
    {
        // 台灣電話號碼格式：02-12345678, 04-12345678, 07-1234567 等
        return preg_match('/^0[2-9]-?\d{7,8}$/', $phone);
    }

    /**
     * 驗證手機號碼
     */
    private function validateMobile($mobile)
    {
        // 台灣手機號碼格式：09xxxxxxxx
        return preg_match('/^09\d{8}$/', $mobile);
    }

    /**
     * 驗證身分證字號
     */
    private function validateIdNumber($idNumber)
    {
        // 台灣身分證字號驗證
        if (!preg_match('/^[A-Z][12]\d{8}$/', $idNumber)) {
            return false;
        }
        
        // 字母對應數字
        $letterMap = [
            'A' => 10, 'B' => 11, 'C' => 12, 'D' => 13, 'E' => 14, 'F' => 15,
            'G' => 16, 'H' => 17, 'I' => 34, 'J' => 18, 'K' => 19, 'L' => 20,
            'M' => 21, 'N' => 22, 'O' => 35, 'P' => 23, 'Q' => 24, 'R' => 25,
            'S' => 26, 'T' => 27, 'U' => 28, 'V' => 29, 'W' => 32, 'X' => 30,
            'Y' => 31, 'Z' => 33
        ];
        
        $letter = substr($idNumber, 0, 1);
        $numbers = substr($idNumber, 1);
        
        $letterValue = $letterMap[$letter];
        $sum = floor($letterValue / 10) + ($letterValue % 10) * 9;
        
        for ($i = 0; $i < 8; $i++) {
            $sum += $numbers[$i] * (8 - $i);
        }
        
        $checksum = (10 - ($sum % 10)) % 10;
        return $checksum == $numbers[8];
    }

    /**
     * 驗證車牌號碼
     */
    private function validateLicensePlate($plate)
    {
        // 台灣車牌格式：ABC-1234, 1234-AB 等
        $patterns = [
            '/^[A-Z]{3}-\d{4}$/',     // 一般車牌：ABC-1234
            '/^\d{4}-[A-Z]{2}$/',     // 機車車牌：1234-AB
            '/^[A-Z]{2}-\d{4}$/',     // 特殊車牌：AB-1234
            '/^\d{3}-[A-Z]{3}$/',     // 特殊車牌：123-ABC
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $plate)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 驗證日期格式
     */
    private function validateDate($date)
    {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * 驗證日期時間格式
     */
    private function validateDateTime($datetime)
    {
        $d = DateTime::createFromFormat('Y-m-d H:i:s', $datetime);
        return $d && $d->format('Y-m-d H:i:s') === $datetime;
    }

    /**
     * 驗證唯一性
     */
    private function validateUnique($field, $value, $table, $allData)
    {
        $db = Yaf_Registry::get('db');
        $tenant = Yaf_Registry::get('current_tenant');
        
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$field} = ?";
        $params = [$value];
        
        // 添加租戶條件
        if ($tenant) {
            $sql .= " AND tenant_id = ?";
            $params[] = $tenant['id'];
        }
        
        // 如果是更新操作，排除當前記錄
        if (isset($allData['id'])) {
            $sql .= " AND id != ?";
            $params[] = $allData['id'];
        }
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['count'] == 0;
    }

    /**
     * 獲取所有錯誤
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * 獲取第一個錯誤訊息
     */
    public function getFirstError()
    {
        foreach ($this->errors as $fieldErrors) {
            return $fieldErrors[0];
        }
        return null;
    }

    /**
     * 檢查是否有錯誤
     */
    public function hasErrors()
    {
        return !empty($this->errors);
    }
}

<?php
/**
 * 基礎模型類別
 * 提供多租戶數據隔離和基本 CRUD 操作
 */
abstract class BaseModel
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $tenantField = 'tenant_id';
    protected $timestamps = true;
    protected $fillable = [];
    protected $hidden = [];
    
    public function __construct()
    {
        $this->db = Yaf_Registry::get('db');
    }

    /**
     * 獲取當前租戶ID
     */
    protected function getCurrentTenantId()
    {
        $tenant = Yaf_Registry::get('current_tenant');
        return $tenant ? $tenant['id'] : null;
    }

    /**
     * 獲取當前用戶ID
     */
    protected function getCurrentUserId()
    {
        $user = Yaf_Registry::get('current_user');
        return $user ? $user['id'] : null;
    }

    /**
     * 建立查詢建構器
     */
    protected function query()
    {
        return new QueryBuilder($this->db, $this->table, $this->tenantField);
    }

    /**
     * 查找單筆記錄
     */
    public function find($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        $params = [$id];
        
        // 添加租戶條件
        if ($this->tenantField && $this->getCurrentTenantId()) {
            $sql .= " AND {$this->tenantField} = ?";
            $params[] = $this->getCurrentTenantId();
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        $result = $stmt->fetch();
        return $result ? $this->hideFields($result) : null;
    }

    /**
     * 查找多筆記錄
     */
    public function findAll($conditions = [], $orderBy = null, $limit = null, $offset = null)
    {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        $where = [];
        
        // 添加租戶條件
        if ($this->tenantField && $this->getCurrentTenantId()) {
            $where[] = "{$this->tenantField} = ?";
            $params[] = $this->getCurrentTenantId();
        }
        
        // 添加其他條件
        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $placeholders = str_repeat('?,', count($value) - 1) . '?';
                $where[] = "{$field} IN ({$placeholders})";
                $params = array_merge($params, $value);
            } else {
                $where[] = "{$field} = ?";
                $params[] = $value;
            }
        }
        
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' AND ', $where);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
            if ($offset) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        $results = $stmt->fetchAll();
        return array_map([$this, 'hideFields'], $results);
    }

    /**
     * 計算記錄數量
     */
    public function count($conditions = [])
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $params = [];
        $where = [];
        
        // 添加租戶條件
        if ($this->tenantField && $this->getCurrentTenantId()) {
            $where[] = "{$this->tenantField} = ?";
            $params[] = $this->getCurrentTenantId();
        }
        
        // 添加其他條件
        foreach ($conditions as $field => $value) {
            $where[] = "{$field} = ?";
            $params[] = $value;
        }
        
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' AND ', $where);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        $result = $stmt->fetch();
        return (int)$result['count'];
    }

    /**
     * 創建記錄
     */
    public function create($data)
    {
        // 過濾可填充字段
        $data = $this->filterFillable($data);
        
        // 添加租戶ID
        if ($this->tenantField && $this->getCurrentTenantId()) {
            $data[$this->tenantField] = $this->getCurrentTenantId();
        }
        
        // 添加時間戳
        if ($this->timestamps) {
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $fields = array_keys($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ({$placeholders})";
        
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute(array_values($data));
        
        if ($result) {
            $id = $this->db->lastInsertId();
            return $this->find($id);
        }
        
        return false;
    }

    /**
     * 更新記錄
     */
    public function update($id, $data)
    {
        // 過濾可填充字段
        $data = $this->filterFillable($data);
        
        // 添加更新時間戳
        if ($this->timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = ?";
        $params = array_values($data);
        $params[] = $id;
        
        // 添加租戶條件
        if ($this->tenantField && $this->getCurrentTenantId()) {
            $sql .= " AND {$this->tenantField} = ?";
            $params[] = $this->getCurrentTenantId();
        }
        
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute($params);
        
        return $result ? $this->find($id) : false;
    }

    /**
     * 刪除記錄
     */
    public function delete($id)
    {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        $params = [$id];
        
        // 添加租戶條件
        if ($this->tenantField && $this->getCurrentTenantId()) {
            $sql .= " AND {$this->tenantField} = ?";
            $params[] = $this->getCurrentTenantId();
        }
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * 軟刪除記錄
     */
    public function softDelete($id)
    {
        return $this->update($id, ['is_active' => 0]);
    }

    /**
     * 過濾可填充字段
     */
    protected function filterFillable($data)
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * 隱藏敏感字段
     */
    protected function hideFields($data)
    {
        if (empty($this->hidden) || !is_array($data)) {
            return $data;
        }
        
        foreach ($this->hidden as $field) {
            unset($data[$field]);
        }
        
        return $data;
    }

    /**
     * 開始事務
     */
    public function beginTransaction()
    {
        return $this->db->beginTransaction();
    }

    /**
     * 提交事務
     */
    public function commit()
    {
        return $this->db->commit();
    }

    /**
     * 回滾事務
     */
    public function rollback()
    {
        return $this->db->rollback();
    }

    /**
     * 執行原始 SQL
     */
    public function raw($sql, $params = [])
    {
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
}

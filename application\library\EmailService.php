<?php
/**
 * 郵件服務
 */
class EmailService
{
    private $config;
    private $smtpHost;
    private $smtpPort;
    private $smtpUsername;
    private $smtpPassword;
    private $fromEmail;
    private $fromName;
    
    public function __construct()
    {
        $this->config = Yaf_Registry::get('config');
        $this->smtpHost = $this->config->mail->smtp_host;
        $this->smtpPort = $this->config->mail->smtp_port;
        $this->smtpUsername = $this->config->mail->smtp_username;
        $this->smtpPassword = $this->config->mail->smtp_password;
        $this->fromEmail = $this->config->mail->from_email;
        $this->fromName = $this->config->mail->from_name;
    }

    /**
     * 發送支付成功郵件
     */
    public function sendPaymentSuccessEmail($toEmail, $data)
    {
        $subject = '支付成功通知 - ' . $data['tenant_name'];
        
        $body = $this->renderTemplate('payment_success', [
            'tenant_name' => $data['tenant_name'],
            'order_no' => $data['order_no'],
            'amount' => number_format($data['amount']),
            'plan_name' => $data['plan_name'],
            'paid_at' => date('Y-m-d H:i:s', strtotime($data['paid_at']))
        ]);
        
        return $this->sendEmail($toEmail, $subject, $body);
    }

    /**
     * 發送支付失敗郵件
     */
    public function sendPaymentFailureEmail($toEmail, $data)
    {
        $subject = '支付失敗通知 - ' . $data['tenant_name'];
        
        $body = $this->renderTemplate('payment_failure', [
            'tenant_name' => $data['tenant_name'],
            'order_no' => $data['order_no'],
            'amount' => number_format($data['amount']),
            'plan_name' => $data['plan_name'],
            'failure_reason' => $data['failure_reason']
        ]);
        
        return $this->sendEmail($toEmail, $subject, $body);
    }

    /**
     * 發送訂閱到期提醒郵件
     */
    public function sendExpirationReminderEmail($toEmail, $data)
    {
        $subject = '訂閱即將到期提醒 - ' . $data['tenant_name'];
        
        $body = $this->renderTemplate('expiration_reminder', [
            'tenant_name' => $data['tenant_name'],
            'plan_name' => $data['plan_name'],
            'expires_at' => date('Y-m-d', strtotime($data['expires_at'])),
            'days_remaining' => $data['days_remaining'],
            'renewal_url' => $data['renewal_url'] ?? ''
        ]);
        
        return $this->sendEmail($toEmail, $subject, $body);
    }

    /**
     * 發送歡迎郵件
     */
    public function sendWelcomeEmail($toEmail, $data)
    {
        $subject = '歡迎使用汽車保養廠管理系統 - ' . $data['tenant_name'];
        
        $body = $this->renderTemplate('welcome', [
            'tenant_name' => $data['tenant_name'],
            'admin_name' => $data['admin_name'],
            'subdomain' => $data['subdomain'],
            'login_url' => $data['login_url'],
            'trial_expires_at' => date('Y-m-d', strtotime($data['trial_expires_at']))
        ]);
        
        return $this->sendEmail($toEmail, $subject, $body);
    }

    /**
     * 發送密碼重設郵件
     */
    public function sendPasswordResetEmail($toEmail, $data)
    {
        $subject = '密碼重設 - 汽車保養廠管理系統';
        
        $body = $this->renderTemplate('password_reset', [
            'user_name' => $data['user_name'],
            'reset_url' => $data['reset_url'],
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 hour'))
        ]);
        
        return $this->sendEmail($toEmail, $subject, $body);
    }

    /**
     * 發送一般郵件
     */
    public function sendEmail($toEmail, $subject, $body, $isHtml = true)
    {
        try {
            // 如果沒有配置 SMTP，記錄到日誌
            if (empty($this->smtpHost)) {
                error_log("Email would be sent to {$toEmail}: {$subject}");
                return true;
            }
            
            // 使用 PHPMailer 或其他郵件庫
            // 這裡使用簡單的 mail() 函數示例
            $headers = [
                'From: ' . $this->fromName . ' <' . $this->fromEmail . '>',
                'Reply-To: ' . $this->fromEmail,
                'X-Mailer: PHP/' . phpversion()
            ];
            
            if ($isHtml) {
                $headers[] = 'MIME-Version: 1.0';
                $headers[] = 'Content-type: text/html; charset=UTF-8';
            }
            
            $result = mail($toEmail, $subject, $body, implode("\r\n", $headers));
            
            if ($result) {
                error_log("Email sent successfully to {$toEmail}: {$subject}");
            } else {
                error_log("Failed to send email to {$toEmail}: {$subject}");
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 渲染郵件範本
     */
    private function renderTemplate($template, $data)
    {
        $templatePath = APPLICATION_PATH . '/views/emails/' . $template . '.phtml';
        
        if (file_exists($templatePath)) {
            // 提取變數到當前作用域
            extract($data);
            
            // 開始輸出緩衝
            ob_start();
            include $templatePath;
            $content = ob_get_clean();
            
            return $content;
        }
        
        // 如果範本不存在，使用預設範本
        return $this->getDefaultTemplate($template, $data);
    }

    /**
     * 獲取預設郵件範本
     */
    private function getDefaultTemplate($template, $data)
    {
        switch ($template) {
            case 'payment_success':
                return "
                <h2>支付成功通知</h2>
                <p>親愛的 {$data['tenant_name']} 用戶：</p>
                <p>您的訂單支付已成功完成！</p>
                <ul>
                    <li>訂單編號：{$data['order_no']}</li>
                    <li>支付金額：NT$ {$data['amount']}</li>
                    <li>訂閱方案：{$data['plan_name']}</li>
                    <li>支付時間：{$data['paid_at']}</li>
                </ul>
                <p>感謝您的使用！</p>
                ";
                
            case 'payment_failure':
                return "
                <h2>支付失敗通知</h2>
                <p>親愛的 {$data['tenant_name']} 用戶：</p>
                <p>很抱歉，您的訂單支付失敗。</p>
                <ul>
                    <li>訂單編號：{$data['order_no']}</li>
                    <li>支付金額：NT$ {$data['amount']}</li>
                    <li>訂閱方案：{$data['plan_name']}</li>
                    <li>失敗原因：{$data['failure_reason']}</li>
                </ul>
                <p>請重新嘗試支付或聯繫客服。</p>
                ";
                
            case 'expiration_reminder':
                return "
                <h2>訂閱即將到期提醒</h2>
                <p>親愛的 {$data['tenant_name']} 用戶：</p>
                <p>您的訂閱將在 {$data['days_remaining']} 天後到期。</p>
                <ul>
                    <li>訂閱方案：{$data['plan_name']}</li>
                    <li>到期日期：{$data['expires_at']}</li>
                </ul>
                <p>請及時續費以免影響服務使用。</p>
                ";
                
            case 'welcome':
                return "
                <h2>歡迎使用汽車保養廠管理系統</h2>
                <p>親愛的 {$data['admin_name']}：</p>
                <p>歡迎註冊汽車保養廠管理系統！</p>
                <ul>
                    <li>保養廠名稱：{$data['tenant_name']}</li>
                    <li>子域名：{$data['subdomain']}</li>
                    <li>試用期至：{$data['trial_expires_at']}</li>
                </ul>
                <p>請點擊以下連結開始使用：<a href=\"{$data['login_url']}\">立即登入</a></p>
                ";
                
            case 'password_reset':
                return "
                <h2>密碼重設</h2>
                <p>親愛的 {$data['user_name']}：</p>
                <p>您申請了密碼重設，請點擊以下連結重設密碼：</p>
                <p><a href=\"{$data['reset_url']}\">重設密碼</a></p>
                <p>此連結將在 {$data['expires_at']} 後失效。</p>
                <p>如果您沒有申請密碼重設，請忽略此郵件。</p>
                ";
                
            default:
                return "<p>郵件內容</p>";
        }
    }

    /**
     * 批量發送郵件
     */
    public function sendBulkEmails($emails, $subject, $body)
    {
        $successCount = 0;
        $failureCount = 0;
        
        foreach ($emails as $email) {
            if ($this->sendEmail($email, $subject, $body)) {
                $successCount++;
            } else {
                $failureCount++;
            }
            
            // 避免發送過快，稍作延遲
            usleep(100000); // 0.1秒
        }
        
        return [
            'success' => $successCount,
            'failure' => $failureCount,
            'total' => count($emails)
        ];
    }

    /**
     * 測試郵件配置
     */
    public function testEmailConfiguration()
    {
        $testEmail = $this->fromEmail;
        $subject = '郵件配置測試';
        $body = '<p>這是一封測試郵件，用於驗證郵件配置是否正確。</p>';
        
        return $this->sendEmail($testEmail, $subject, $body);
    }
}

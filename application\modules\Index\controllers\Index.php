<?php
/**
 * 首頁控制器
 */
class IndexController extends BaseController
{
    /**
     * 首頁
     */
    public function indexAction()
    {
        // 如果用戶已登入，重定向到儀表板
        if ($this->user) {
            return $this->redirect('/dashboard');
        }
        
        // 顯示首頁
        $this->getView()->assign('title', '汽車保養廠管理系統');
    }

    /**
     * 儀表板
     */
    public function dashboardAction()
    {
        if (!$this->user) {
            return $this->redirect('/auth/login');
        }
        
        // 獲取統計數據
        $stats = $this->getDashboardStats();
        
        $this->getView()->assign('stats', $stats);
        $this->getView()->assign('title', '儀表板');
    }

    /**
     * 獲取儀表板統計數據
     */
    private function getDashboardStats()
    {
        $stats = [];
        
        if (!$this->tenant) {
            return $stats;
        }
        
        try {
            // 租戶統計
            $tenantModel = new TenantModel();
            $stats['tenant'] = $tenantModel->getStats($this->tenant['id']);
            
            // 今日工單
            $workOrderModel = new WorkOrderModel();
            $stats['today_orders'] = $workOrderModel->getTodayOrders();
            
            // 待處理工單
            $stats['pending_orders'] = $workOrderModel->getPendingOrders();
            
            // 本週預約
            $appointmentModel = new AppointmentModel();
            $stats['week_appointments'] = $appointmentModel->getWeekAppointments();
            
            // 低庫存警示
            $partModel = new PartModel();
            $stats['low_stock_parts'] = $partModel->getLowStockParts();
            
            // 近期到期提醒
            $stats['expiring_reminders'] = $this->getExpiringReminders();
            
        } catch (Exception $e) {
            error_log('Dashboard stats error: ' . $e->getMessage());
        }
        
        return $stats;
    }

    /**
     * 獲取到期提醒
     */
    private function getExpiringReminders()
    {
        $reminders = [];
        
        // 檢查訂閱到期
        if ($this->tenant['subscription_expires_at']) {
            $expiresAt = new DateTime($this->tenant['subscription_expires_at']);
            $now = new DateTime();
            $diff = $expiresAt->diff($now);
            
            if ($diff->days <= 7 && $expiresAt > $now) {
                $reminders[] = [
                    'type' => 'subscription',
                    'message' => "訂閱將在 {$diff->days} 天後到期",
                    'level' => $diff->days <= 3 ? 'danger' : 'warning'
                ];
            }
        }
        
        return $reminders;
    }

    /**
     * API 入口
     */
    public function apiAction()
    {
        $resource = $this->getParam('resource');
        $method = $this->getParam('method');
        $params = $this->getParam('params');
        
        // 解析參數
        $pathParams = $params ? explode('/', trim($params, '/')) : [];
        
        try {
            $apiController = $this->getApiController($resource);
            if (!$apiController) {
                return $this->error('API 資源不存在', 404);
            }
            
            $result = $apiController->handleRequest($method, $pathParams);
            return $this->jsonResponse($result);
            
        } catch (Exception $e) {
            return $this->error($e->getMessage(), 500);
        }
    }

    /**
     * 獲取 API 控制器
     */
    private function getApiController($resource)
    {
        $controllerMap = [
            'customers' => 'CustomerApiController',
            'vehicles' => 'VehicleApiController',
            'workorders' => 'WorkOrderApiController',
            'parts' => 'PartApiController',
            'appointments' => 'AppointmentApiController',
            'users' => 'UserApiController',
            'reports' => 'ReportApiController'
        ];
        
        if (!isset($controllerMap[$resource])) {
            return null;
        }
        
        $className = $controllerMap[$resource];
        if (class_exists($className)) {
            return new $className();
        }
        
        return null;
    }

    /**
     * 搜索功能
     */
    public function searchAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        $query = $this->getParam('q', '');
        $type = $this->getParam('type', 'all');
        
        if (strlen($query) < 2) {
            return $this->success([]);
        }
        
        $results = $this->performSearch($query, $type);
        return $this->success($results);
    }

    /**
     * 執行搜索
     */
    private function performSearch($query, $type)
    {
        $results = [];
        
        if ($type === 'all' || $type === 'customers') {
            $customerModel = new CustomerModel();
            $customers = $customerModel->search($query);
            foreach ($customers as $customer) {
                $results[] = [
                    'type' => 'customer',
                    'id' => $customer['id'],
                    'title' => $customer['name'],
                    'subtitle' => $customer['phone'],
                    'url' => '/customers/view/' . $customer['id']
                ];
            }
        }
        
        if ($type === 'all' || $type === 'vehicles') {
            $vehicleModel = new VehicleModel();
            $vehicles = $vehicleModel->search($query);
            foreach ($vehicles as $vehicle) {
                $results[] = [
                    'type' => 'vehicle',
                    'id' => $vehicle['id'],
                    'title' => $vehicle['license_plate'],
                    'subtitle' => $vehicle['brand'] . ' ' . $vehicle['model'],
                    'url' => '/vehicles/view/' . $vehicle['id']
                ];
            }
        }
        
        if ($type === 'all' || $type === 'workorders') {
            $workOrderModel = new WorkOrderModel();
            $workOrders = $workOrderModel->search($query);
            foreach ($workOrders as $order) {
                $results[] = [
                    'type' => 'workorder',
                    'id' => $order['id'],
                    'title' => $order['order_no'],
                    'subtitle' => $order['customer_name'] . ' - ' . $order['license_plate'],
                    'url' => '/workorders/view/' . $order['id']
                ];
            }
        }
        
        return $results;
    }

    /**
     * 通知中心
     */
    public function notificationsAction()
    {
        if (!$this->isAjax()) {
            return $this->error('僅支援 AJAX 請求');
        }
        
        $notifications = $this->getNotifications();
        return $this->success($notifications);
    }

    /**
     * 獲取通知
     */
    private function getNotifications()
    {
        $notifications = [];
        
        // 低庫存通知
        $partModel = new PartModel();
        $lowStockParts = $partModel->getLowStockParts(5); // 只取前5個
        foreach ($lowStockParts as $part) {
            $notifications[] = [
                'type' => 'warning',
                'title' => '庫存不足',
                'message' => "{$part['name']} 庫存僅剩 {$part['stock_quantity']} {$part['unit']}",
                'time' => date('Y-m-d H:i:s'),
                'url' => '/parts/view/' . $part['id']
            ];
        }
        
        // 待處理工單通知
        $workOrderModel = new WorkOrderModel();
        $pendingOrders = $workOrderModel->getPendingOrders(3); // 只取前3個
        foreach ($pendingOrders as $order) {
            $notifications[] = [
                'type' => 'info',
                'title' => '待處理工單',
                'message' => "工單 {$order['order_no']} 等待處理",
                'time' => $order['created_at'],
                'url' => '/workorders/view/' . $order['id']
            ];
        }
        
        // 今日預約通知
        $appointmentModel = new AppointmentModel();
        $todayAppointments = $appointmentModel->getTodayAppointments(3); // 只取前3個
        foreach ($todayAppointments as $appointment) {
            $notifications[] = [
                'type' => 'success',
                'title' => '今日預約',
                'message' => "{$appointment['customer_name']} - {$appointment['appointment_time']}",
                'time' => $appointment['appointment_date'] . ' ' . $appointment['appointment_time'],
                'url' => '/appointments/view/' . $appointment['id']
            ];
        }
        
        // 按時間排序
        usort($notifications, function($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });
        
        return array_slice($notifications, 0, 10); // 最多返回10個通知
    }
}

-- 租戶表
CREATE TABLE tenants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    domain VARCHAR(50) UNIQUE,
    subscription_plan ENUM('basic', 'pro', 'enterprise'),
    expires_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用戶表（多租戶）
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    role ENUM('admin', 'manager', 'technician'),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);
<?php
/**
 * 基礎控制器
 * 提供通用的控制器功能
 */
abstract class BaseController extends Yaf_Controller_Abstract
{
    protected $tenant;
    protected $user;
    protected $config;

    /**
     * 初始化
     */
    public function init()
    {
        $this->config = Yaf_Registry::get('config');
        $this->tenant = Yaf_Registry::get('current_tenant');
        $this->user = Yaf_Registry::get('current_user');
        
        // 設定視圖變數
        $this->getView()->assign('tenant', $this->tenant);
        $this->getView()->assign('user', $this->user);
        $this->getView()->assign('config', $this->config);
    }

    /**
     * 檢查權限
     */
    protected function checkPermission($permission)
    {
        if (!$this->user) {
            $this->jsonResponse(['success' => false, 'message' => '請先登入']);
            return false;
        }
        
        // 檢查用戶角色權限
        $userRole = $this->user['role'];
        $permissions = $this->getRolePermissions($userRole);
        
        if (!in_array($permission, $permissions)) {
            $this->jsonResponse(['success' => false, 'message' => '權限不足']);
            return false;
        }
        
        return true;
    }

    /**
     * 獲取角色權限
     */
    protected function getRolePermissions($role)
    {
        $permissions = [
            'owner' => ['*'], // 擁有者有所有權限
            'admin' => [
                'user.view', 'user.create', 'user.edit', 'user.delete',
                'customer.view', 'customer.create', 'customer.edit', 'customer.delete',
                'vehicle.view', 'vehicle.create', 'vehicle.edit', 'vehicle.delete',
                'workorder.view', 'workorder.create', 'workorder.edit', 'workorder.delete',
                'part.view', 'part.create', 'part.edit', 'part.delete',
                'appointment.view', 'appointment.create', 'appointment.edit', 'appointment.delete',
                'payment.view', 'payment.create',
                'report.view', 'setting.view', 'setting.edit'
            ],
            'manager' => [
                'customer.view', 'customer.create', 'customer.edit',
                'vehicle.view', 'vehicle.create', 'vehicle.edit',
                'workorder.view', 'workorder.create', 'workorder.edit',
                'part.view', 'part.create', 'part.edit',
                'appointment.view', 'appointment.create', 'appointment.edit',
                'payment.view', 'payment.create',
                'report.view'
            ],
            'technician' => [
                'customer.view',
                'vehicle.view',
                'workorder.view', 'workorder.edit',
                'part.view',
                'appointment.view'
            ],
            'staff' => [
                'customer.view',
                'vehicle.view',
                'workorder.view',
                'appointment.view', 'appointment.create', 'appointment.edit'
            ]
        ];
        
        $userPermissions = $permissions[$role] ?? [];
        
        // 如果有 * 權限，表示擁有所有權限
        if (in_array('*', $userPermissions)) {
            return ['*'];
        }
        
        return $userPermissions;
    }

    /**
     * 檢查是否有權限
     */
    protected function hasPermission($permission)
    {
        if (!$this->user) {
            return false;
        }
        
        $userRole = $this->user['role'];
        $permissions = $this->getRolePermissions($userRole);
        
        return in_array('*', $permissions) || in_array($permission, $permissions);
    }

    /**
     * 返回 JSON 響應
     */
    protected function jsonResponse($data, $statusCode = 200)
    {
        $this->getResponse()->setHeader('Content-Type', 'application/json; charset=utf-8');
        $this->getResponse()->setHeader('HTTP/1.1', $statusCode);
        
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        return false; // 阻止視圖渲染
    }

    /**
     * 成功響應
     */
    protected function success($data = null, $message = '操作成功')
    {
        return $this->jsonResponse([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 錯誤響應
     */
    protected function error($message = '操作失敗', $code = 400, $data = null)
    {
        return $this->jsonResponse([
            'success' => false,
            'message' => $message,
            'code' => $code,
            'data' => $data
        ], $code);
    }

    /**
     * 驗證請求數據
     */
    protected function validate($data, $rules)
    {
        $validator = new Validator();
        return $validator->validate($data, $rules);
    }

    /**
     * 獲取請求參數
     */
    protected function getParam($key, $default = null)
    {
        return $this->getRequest()->getParam($key, $default);
    }

    /**
     * 獲取 POST 數據
     */
    protected function getPost($key = null, $default = null)
    {
        if ($key === null) {
            return $_POST;
        }
        return $_POST[$key] ?? $default;
    }

    /**
     * 獲取 GET 數據
     */
    protected function getGet($key = null, $default = null)
    {
        if ($key === null) {
            return $_GET;
        }
        return $_GET[$key] ?? $default;
    }

    /**
     * 獲取 JSON 輸入
     */
    protected function getJsonInput()
    {
        $input = file_get_contents('php://input');
        return json_decode($input, true);
    }

    /**
     * 檢查是否為 AJAX 請求
     */
    protected function isAjax()
    {
        return $this->getRequest()->isXmlHttpRequest();
    }

    /**
     * 檢查是否為 POST 請求
     */
    protected function isPost()
    {
        return $this->getRequest()->isPost();
    }

    /**
     * 檢查是否為 GET 請求
     */
    protected function isGet()
    {
        return $this->getRequest()->isGet();
    }

    /**
     * 重定向
     */
    protected function redirect($url, $statusCode = 302)
    {
        $this->getResponse()->setRedirect($url, $statusCode);
        return false;
    }

    /**
     * 記錄操作日誌
     */
    protected function logActivity($action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null)
    {
        if (!$this->tenant || !$this->user) {
            return;
        }
        
        $db = Yaf_Registry::get('db');
        
        $logData = [
            'tenant_id' => $this->tenant['id'],
            'user_id' => $this->user['id'],
            'action' => $action,
            'table_name' => $tableName,
            'record_id' => $recordId,
            'old_values' => $oldValues ? json_encode($oldValues, JSON_UNESCAPED_UNICODE) : null,
            'new_values' => $newValues ? json_encode($newValues, JSON_UNESCAPED_UNICODE) : null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];
        
        $fields = array_keys($logData);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO activity_logs (" . implode(',', $fields) . ") VALUES ({$placeholders})";
        $stmt = $db->prepare($sql);
        $stmt->execute(array_values($logData));
    }

    /**
     * 分頁處理
     */
    protected function paginate($query, $page = 1, $perPage = 15)
    {
        $page = max(1, (int)$page);
        $perPage = max(1, min(100, (int)$perPage)); // 限制每頁最多100條
        
        $total = $query->count();
        $totalPages = ceil($total / $perPage);
        
        $data = $query->paginate($page, $perPage)->get();
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages
            ]
        ];
    }

    /**
     * 生成 CSRF Token
     */
    protected function generateCsrfToken()
    {
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        return $token;
    }

    /**
     * 驗證 CSRF Token
     */
    protected function validateCsrfToken($token)
    {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}
